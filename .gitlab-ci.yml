stages:
  - package
  - publish

variables:
  NUGET_URL: "https://api.nuget.org/v3/index.json"

before_script:
  - apt-get update && apt-get install -y mono-complete
  - wget https://dist.nuget.org/win-x86-commandline/latest/nuget.exe
  - alias nuget='mono nuget.exe'

package:
  stage: package
  script:
    - mono nuget.exe pack ArabDt.BuildingBlocks.V3.nuspec -NoDefaultExcludes
  artifacts:
    paths:
      - "*.nupkg"

publish:
  stage: publish
  script:
    - mono nuget.exe push *.nupkg -Source "$NUGET_URL" -ApiKey "$NUGET_API_KEY" -SkipDuplicate
  only:
    - main
  dependencies:
    - package
