{"$schema": "http://json.schemastore.org/vs-2017.3.host", "order": 0, "icon": "icon.png", "symbolInfo": [{"id": "ClientFramework", "name": {"text": "Client Framework"}, "description": {"text": "Select the Client Framework type, or select None for Web API only."}, "isVisible": true}, {"id": "PipelineProvider", "name": {"text": "Pipeline Provider"}, "description": {"text": "Select the pipeline provider."}, "isVisible": true}, {"id": "Database", "name": {"text": "Database"}, "description": {"text": "Select the database type."}, "isVisible": true}, {"id": "UseAspire", "name": {"text": "Use Aspire"}, "description": {"text": "Use .NET Aspire"}, "isVisible": true}]}