{"$schema": "http://json.schemastore.org/template", "author": "ArabDt", "classifications": ["Aspire", "Web", "Angular", "ASP.NET", "Clean Architecture", "Onion Architecture"], "name": "ArabDt.BuildingBlocks.Template", "description": "A Reusable Solution Template for creating Clean & Onion Architecture Application ASP.NET Core.", "identity": "ArabDt.BuildingBlocks.Template.CSharp", "groupIdentity": "ArabDt.BuildingBlocks.Template", "shortName": "arabdt-template", "tags": {"language": "C#", "type": "project"}, "sourceName": "ArabDt", "preferNameDirectory": true, "symbols": {"name": {"type": "parameter", "datatype": "string", "isRequired": true, "description": "The name of the project/class/thing", "replaces": "ArabDt"}, "ArabDtPackageVersion": {"type": "generated", "generator": "constant", "replaces": "adPackageVersion", "parameters": {"value": "9.0.3"}}, "ArabDtRepositoryUrl": {"type": "generated", "generator": "constant", "replaces": "adRepositoryUrl", "parameters": {"value": "https://gitlab.com/AElbaradey/arabdt.template"}}, "ClientFramework": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "Angular", "description": "Use Angular"}, {"choice": "React", "description": "Use React"}, {"choice": "None", "description": "Web API only"}], "defaultValue": "None", "description": "The type of client framework to use"}, "UseAngular": {"type": "computed", "value": "(ClientFramework == \"Angular\")"}, "UseReact": {"type": "computed", "value": "(ClientFramework == \"React\")"}, "UseApiOnly": {"type": "computed", "value": "(ClientFramework == \"None\")"}, "UseAspire": {"type": "parameter", "datatype": "bool", "defaultValue": "false", "description": "Use .NET Aspire"}, "Database": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "postgresql", "description": "PostgreSQL"}, {"choice": "sqlite", "description": "SQLite"}, {"choice": "sqlserver", "description": "SQL Server"}], "defaultValue": "sqlserver", "description": "The database type to use."}, "PipelineProvider": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "a<PERSON>do", "description": "Azure Pipelines"}, {"choice": "github", "description": "GitHub Actions"}], "defaultValue": "github", "description": "The pipeline provider to use (github for Github Actions and azdo for Azure Pipelines)."}, "UseAzurePipelines": {"type": "computed", "value": "(PipelineProvider == \"azdo\")"}, "UseGithubActions": {"type": "computed", "value": "(PipelineProvider == \"github\")"}, "UsePostgreSQL": {"type": "computed", "value": "(Database == \"postgresql\")"}, "UseSqlite": {"type": "computed", "value": "(Database == \"sqlite\")"}, "UseSqlServer": {"type": "computed", "value": "(Database == \"sqlserver\")"}}, "sources": [{"source": "./", "target": "./", "exclude": [".azure/**/*", ".template.config/**/*", "templates/**/*", "**/*.filelist", "**/*.user", "**/*.lock.json", "**/bin/**/*", "**/obj/**/*", "*.nuspec", "appsettings.json", "src/Infrastructure/Data/Migrations/**", "src/Infrastructure/Data/SQLite/**", "src/Infrastructure/Data/PostgreSQL/**", "src/Web/appsettings.Development.json", "src/Web/appsettings.PostgreSQL.json", "src/Web/appsettings.SQLite.json", "tests/Application.FunctionalTests/PostgreSQLTestcontainersTestDatabase.cs", "tests/Application.FunctionalTests/PostgreSQLTestDatabase.cs", "tests/Application.FunctionalTests/SqliteTestDatabase.cs", "tests/Application.FunctionalTests/SqlTestcontainersTestDatabase.cs", "tests/Application.FunctionalTests/SqlTestDatabase.cs", "tests/Application.FunctionalTests/appsettings.json", "tests/Application.FunctionalTests/appsettings.PostgreSQL.json", ".azdo/**/*", ".github/**/*", ".git/**/*"], "rename": {"README-template.md": "README.md"}, "modifiers": [{"condition": "(UseAngular)", "exclude": ["src/Web/ClientApp-React/**", "src/Web/Endpoints/Users.cs", "src/Web/Templates/**", "src/Web/config-react.nswag", "src/Web/config-webapi.nswag", "src/Web/Web-webapi.http", "src/Web/wwwroot/api/specification-webapi.json"]}, {"condition": "(UseReact)", "exclude": ["src/Web/ClientApp/**", "src/Web/Endpoints/Users.cs", "src/Web/config.nswag", "src/Web/config-webapi.nswag", "src/Web/Web-webapi.http", "src/Web/wwwroot/api/specification-webapi.json"], "rename": {"config-react.nswag": "config.nswag", "ClientApp-React": "ClientApp"}}, {"condition": "(UseApiOnly)", "exclude": ["src/Web/ClientApp/**", "src/Web/ClientApp-React/**", "src/Web/Pages/**", "src/Web/Templates/**", "src/Web/config.nswag", "src/Web/config-react.nswag", "src/Web/Web.http", "tests/Web.AcceptanceTests/**", "src/Web/wwwroot/api/specification.json"], "rename": {"config-webapi.nswag": "config.nswag", "Web-webapi.http": "Web.http", "specification-webapi.json": "specification.json"}}, {"condition": "(UseAzurePipelines)", "include": [".azdo/**/*"]}, {"condition": "(UseGithubActions)", "include": [".github/**/*"]}, {"condition": "(UsePostgreSQL)", "include": ["src/Infrastructure/Data/PostgreSQL/**", "src/Web/appsettings.PostgreSQL.json", "tests/Application.FunctionalTests/PostgreSQLTestcontainersTestDatabase.cs", "tests/Application.FunctionalTests/PostgreSQLTestDatabase.cs", "tests/Application.FunctionalTests/appsettings.PostgreSQL.json"], "rename": {"src/Infrastructure/Data/PostgreSQL/": "src/Infrastructure/Data/Migrations/", "src/Web/appsettings.PostgreSQL.json": "src/Web/appsettings.Development.json", "tests/Application.FunctionalTests/appsettings.PostgreSQL.json": "tests/Application.FunctionalTests/appsettings.json"}}, {"condition": "(UseSqlServer)", "include": ["src/Infrastructure/Data/Migrations/**", "src/Web/appsettings.Development.json", "tests/Application.FunctionalTests/SqlTestcontainersTestDatabase.cs", "tests/Application.FunctionalTests/SqlTestDatabase.cs", "tests/Application.FunctionalTests/appsettings.json"]}, {"condition": "(UseSqlite)", "include": ["src/Infrastructure/Data/SQLite/**", "src/Web/appsettings.SQLite.json", "tests/Application.FunctionalTests/SqliteTestDatabase.cs"], "exclude": ["tests/Application.FunctionalTests/appsettings.json"], "rename": {"src/Infrastructure/Data/SQLite/": "src/Infrastructure/Data/Migrations/", "src/Web/appsettings.SQLite.json": "src/Web/appsettings.Development.json"}}, {"condition": "(!UseAspire)", "exclude": ["src/AppHost/**", "src/ServiceDefaults/**"]}]}]}