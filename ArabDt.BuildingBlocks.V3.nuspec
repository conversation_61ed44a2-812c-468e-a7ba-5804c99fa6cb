<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>

    <id>ArabDt.Template</id>
    <version>9.0.4</version>
    <title>Clean and Onion Architecture Solution Template</title>
    <authors>Arab Dt</authors>
    <description>Clean and Onion Architecture Solution Template for .NET 9.0</description>
    <summary>
      A Solution Template for creating apps using Clean and Onion Architecture , Angular or Web API only with ASP.NET Core.
    </summary>
    <releaseNotes>
      📦 Upgrade to .NET 9.0
      🐛 Bugs fixes
    </releaseNotes>

    <projectUrl>https://gitlab.com/AElbaradey/arabdt.template</projectUrl>
    <repository type="git" url="https://gitlab.com/AElbaradey/arabdt.template.git" branch="test" />

    <license type="expression">MIT</license>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <tags>clean-architecture onion-architecture  project template csharp dotnet angular react</tags>
    <icon>icon.png</icon>
    <readme>README.md</readme>

    <packageTypes>
      <packageType name="Template" />
    </packageTypes>

  </metadata>

  <files>
    <file src=".template.config\icon.png" />
    <file src="README.md" />
    <file src=".\**"
	target="content" exclude="**\node_modules\**;**\tools\**;**\bin\**;**\obj\**;.\.vs\**;.\.vscode\**;**\ClientApp\dist\**;**\wwwroot\dist\**;content\Directory.Build.*;.\.git\**;.\.github\workflows\package.yml;.\.github\workflows\codeql.yml;.\.github\workflows\build.yml;.\.github\ISSUE_TEMPLATE\**;.\.github\icon.png;.\.github\FUNDING.md;.\CODE_OF_CONDUCT.md;.\LICENSE;.\README.md;.\CleanArchitecture.nuspec;.\src\Web\app.db;.\build.cake;.\src\apis\Main\appsettings.json" />
  </files>

</package>
