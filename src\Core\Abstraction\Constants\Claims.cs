﻿
namespace Abstraction.Constants
{
    public  static partial class Claims
    {
         
        public static List<string> GenerateModules()
        {
            return new List<string>()
            {
                "Product",
            };
        }

        public static List<string> GeneratePermissions(string module)
        {
            return new List<string>()
            {
                $"{module}.View",
                $"{module}.List",
                $"{module}.Create",
                $"{module}.Edit",
                $"{module}.Delete"
            };
        }
        
    }
}