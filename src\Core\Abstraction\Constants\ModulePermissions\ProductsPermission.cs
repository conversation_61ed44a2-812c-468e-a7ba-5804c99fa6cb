﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abstraction.Constants.ModulePermissions
{
    public static class ProductPermission
    {
        public const string View = "Product.View";
        public const string List = "Product.List";
        public const string Create = "Product.Create";
        public const string Edit = "Product.Edit";
        public const string Delete = "Product.Delete";
    }
 
}
