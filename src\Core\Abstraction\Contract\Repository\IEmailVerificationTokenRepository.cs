using Domain.Entities.Users;

namespace Abstraction.Contracts.Repository
{
    public interface IEmailVerificationTokenRepository
    {
        Task<EmailVerificationToken?> GetByTokenAsync(string token);
        Task<EmailVerificationToken?> GetByUserIdAsync(int userId, bool onlyUnused = true);
        Task<EmailVerificationToken> CreateAsync(EmailVerificationToken token);
        Task UpdateAsync(EmailVerificationToken token);
        Task<bool> IsTokenValidAsync(string token);
        Task<List<EmailVerificationToken>> GetExpiredTokensAsync();
        Task DeleteExpiredTokensAsync();
    }
}
