using Domain.Entities.Users;

namespace Abstraction.Contracts.Repository
{
    public interface IPasswordResetTokenRepository
    {
        Task<PasswordResetToken?> GetByTokenAsync(string token);
        Task<PasswordResetToken?> GetByUserIdAsync(int userId, bool onlyUnused = true);
        Task<PasswordResetToken> CreateAsync(PasswordResetToken token);
        Task UpdateAsync(PasswordResetToken token);
        Task<bool> IsTokenValidAsync(string token);
        Task<List<PasswordResetToken>> GetExpiredTokensAsync();
        Task DeleteExpiredTokensAsync();
        Task<int> GetRecentAttemptsCountAsync(string email, TimeSpan timeSpan);
    }
}
