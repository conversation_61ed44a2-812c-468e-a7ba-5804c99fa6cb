namespace Abstraction.Contracts.Service
{
    public interface IEmailService
    {
        Task<bool> SendEmailVerificationAsync(string email, string fullName, string token, string callbackUrl);
        Task<bool> SendPasswordResetAsync(string email, string fullName, string token, string callbackUrl);
        Task<bool> SendWelcomeEmailAsync(string email, string fullName);
        Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true);
    }
}
