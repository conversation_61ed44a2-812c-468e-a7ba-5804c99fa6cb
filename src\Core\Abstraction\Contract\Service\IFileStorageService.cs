using Microsoft.AspNetCore.Http;

namespace Abstraction.Contracts.Service
{
    public interface IFileStorageService
    {
        Task<FileUploadResult> UploadAvatarAsync(IFormFile file, int userId);
        Task<bool> DeleteAvatarAsync(string fileUrl);
        Task<bool> FileExistsAsync(string fileUrl);
        string GetFullUrl(string relativePath);
    }

    public class FileUploadResult
    {
        public bool Success { get; set; }
        public string? FileUrl { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? ErrorMessage { get; set; }
        public long FileSize { get; set; }
        public string? FileName { get; set; }
    }
}
