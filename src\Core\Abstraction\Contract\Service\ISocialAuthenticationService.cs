using Domain.Entities.Users;

namespace Abstraction.Contracts.Service
{
    public interface ISocialAuthenticationService
    {
        Task<string> GetAuthorizationUrlAsync(string provider, string state, string? returnUrl = null);
        Task<SocialUserInfo?> GetUserInfoAsync(string provider, string code, string state);
        Task<UserExternalLogin?> CreateOrUpdateExternalLoginAsync(User user, SocialUserInfo socialUserInfo, string provider);
        Task<User?> FindUserByExternalLoginAsync(string provider, string providerKey);
    }

    public class SocialUserInfo
    {
        public string Id { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string? ProfilePictureUrl { get; set; }
        public string Provider { get; set; } = null!;
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? TokenExpiresAt { get; set; }
    }
}
