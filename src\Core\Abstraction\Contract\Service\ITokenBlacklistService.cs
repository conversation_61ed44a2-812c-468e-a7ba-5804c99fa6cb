namespace Abstraction.Contracts.Service
{
    public interface ITokenBlacklistService
    {
        Task BlacklistTokenAsync(string jti, DateTime expiry);
        Task<bool> IsTokenBlacklistedAsync(string jti);
        Task CleanupExpiredTokensAsync();
        Task BlacklistAllUserTokensAsync(int userId);
        Task<bool> IsUserTokenValidAsync(int userId, string jti);
        Task TrackUserTokenAsync(int userId, string jti, DateTime expiry);
    }
}
