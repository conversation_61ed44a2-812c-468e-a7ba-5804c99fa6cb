﻿using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
namespace Abstraction.Contracts.Identity
{
    public interface  IUserManagmentService
    {
        public Task<User?> FindByEmailAsync(string email);
        public Task<User?> FindByNameAsync(string userName);
        public Task<IdentityResult> CreateAsync(User user, string password);
        public Task<User?> FindByIdAsync(string id);
        public Task<IdentityResult> ChangePasswordAsync(User user, string currentPassword, string newPassword);
        public Task<IdentityResult> DeleteAsync(User user);
        public Task<IdentityResult> UpdateAsync(User user);
        public Task<IdentityResult> AddToRoleAsync(User user, string role);
        public Task<string> GeneratePasswordResetTokenAsync(User user);
        public Task<IdentityResult> ResetPasswordAsync(User user, string token, string newPassword);
        public Task<IdentityResult> UpdateSecurityStampAsync(User user);
        public IQueryable<User> Users();
    }
}
