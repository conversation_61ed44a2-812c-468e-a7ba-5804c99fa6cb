﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
	  <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" />
	  <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
	  <PackageReference Include="AutoMapper" />
	  <PackageReference Include="FluentValidation" />
	  <PackageReference Include="MediatR" />
	  <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
	  <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" />
	  <PackageReference Include="System.Linq.Dynamic.Core" />
	  <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />	  
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Abstraction\Abstraction.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Resources\Resources.csproj" />
  </ItemGroup>

</Project>
