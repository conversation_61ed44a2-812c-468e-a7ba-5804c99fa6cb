﻿using System.Reflection;
using Application.Common.Behaviors;
using Application.Common.Configurations;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Application
{
    public static class ApplicationDependencies
    {
        public static IServiceCollection AddApplicationModuleDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddMediatR(o => o.RegisterServicesFromAssemblies(Assembly.GetExecutingAssembly()));
            services.AddAutoMapper(Assembly.GetExecutingAssembly());
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            services.Configure<PDFConfiguration>(configuration.GetSection("Pdf"));
            return services;
        }
    }
}
