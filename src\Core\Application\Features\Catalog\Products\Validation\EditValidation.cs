﻿using Application.Features.Catalog.Products.Commands.Edit;
using FluentValidation;

namespace Application.Features.Catalog.Products.Validation
{
    public class EditValidation : AbstractValidator<EditProductCommand>
    {
        public EditValidation()
        {
            Include(new BaseValidation());
            RuleFor(x => x.CategoryId)
                   .NotEmpty().WithMessage("Category Id can't be empty.")
                   .NotNull().WithMessage("Category Id can't be blank.");
        }
    }
}
