using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Domain.Helpers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Application.Features.Identity.Authentications.Commands.ForgotPassword
{
    public class ForgotPasswordCommandHandler : Base<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, IRequestHandler<ForgotPasswordCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly ILoggerManager _logger;
        private readonly IPasswordResetTokenRepository _tokenRepository;
        private readonly IEmailService _emailService;
        private readonly ITokenService _tokenService;
        private readonly SecuritySettings _securitySettings;
        private readonly EmailSettings _emailSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;
        #endregion

        #region Constructor
        public ForgotPasswordCommandHandler(
            IIdentityServiceManager service,
            ILoggerManager logger,
            IPasswordResetTokenRepository tokenRepository,
            IEmailService emailService,
            ITokenService tokenService,
            IOptions<SecuritySettings> securitySettings,
            IOptions<EmailSettings> emailSettings,
            IHttpContextAccessor httpContextAccessor)
        {
            _service = service;
            _logger = logger;
            _tokenRepository = tokenRepository;
            _emailService = emailService;
            _tokenService = tokenService;
            _securitySettings = securitySettings.Value;
            _emailSettings = emailSettings.Value;
            _httpContextAccessor = httpContextAccessor;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(ForgotPasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Always return success message to prevent user enumeration
                var successMessage = "If an account with that email address exists, we have sent you a password reset link.";

                // Find user by email
                var user = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (user == null)
                {
                    _logger.LogInfo($"Password reset requested for non-existent email: {request.Email}");
                    return Success<string>(successMessage);
                }

                // Check if user's email is confirmed
                if (!user.EmailConfirmed)
                {
                    _logger.LogInfo($"Password reset requested for unconfirmed email: {request.Email}");
                    return Success<string>(successMessage);
                }

                // Check rate limiting
                var recentAttempts = await _tokenRepository.GetRecentAttemptsCountAsync(
                    request.Email, 
                    TimeSpan.FromHours(1));

                if (recentAttempts >= _securitySettings.MaxPasswordResetAttemptsPerHour)
                {
                    _logger.LogWarn($"Rate limit exceeded for password reset: {request.Email}");
                    return BadRequest<string>("Too many password reset attempts. Please try again later.");
                }

                // Check for existing unused token
                var existingToken = await _tokenRepository.GetByUserIdAsync(user.Id, onlyUnused: true);
                if (existingToken != null)
                {
                    // If token is still valid (not expired), don't create a new one
                    if (existingToken.ExpiryDate > DateTime.UtcNow)
                    {
                        _logger.LogInfo($"Existing valid password reset token found for user: {user.Email}");
                        return Success<string>(successMessage);
                    }
                    
                    // Mark expired token as used
                    existingToken.IsUsed = true;
                    await _tokenRepository.UpdateAsync(existingToken);
                }

                // Generate new password reset token
                var token = _tokenService.GeneratePasswordResetToken();
                var passwordResetToken = new PasswordResetToken
                {
                    UserId = user.Id,
                    Token = token,
                    ExpiryDate = DateTime.UtcNow.AddHours(_securitySettings.PasswordResetTokenExpiryHours),
                    IsUsed = false,
                    CreatedAt = DateTime.UtcNow,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent()
                };

                // Save token to database
                await _tokenRepository.CreateAsync(passwordResetToken);

                // Send password reset email
                var callbackUrl = $"{_emailSettings.BaseUrl}/api/Users/<USER>/Reset-Password?token={token}&email={user.Email}";
                var emailSent = await _emailService.SendPasswordResetAsync(user.Email, user.FullName, token, callbackUrl);

                if (!emailSent)
                {
                    _logger.LogWarn($"Failed to send password reset email to {user.Email}");
                    return ServerError<string>("Failed to send password reset email. Please try again later.");
                }

                _logger.LogInfo($"Password reset email sent to {user.Email}");
                return Success<string>(successMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while processing forgot password request");
                return ServerError<string>("An error occurred while processing your request. Please try again.");
            }
        }
        #endregion

        #region Helper Methods
        private string? GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return null;

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress;
        }

        private string? GetUserAgent()
        {
            var context = _httpContextAccessor.HttpContext;
            return context?.Request.Headers["User-Agent"].FirstOrDefault();
        }
        #endregion
    }
}
