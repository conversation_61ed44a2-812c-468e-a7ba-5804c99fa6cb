using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Authentications.Commands.Register
{
    public record RegisterUserCommand : ICommand<BaseResponse<string>>
    {
        public string FullName { get; set; } = null!;
        public string UserName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string Password { get; set; } = null!;
        public string ConfirmPassword { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Country { get; set; }
        public string? Address { get; set; }
    }
}
