using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using AutoMapper;
using Domain.Entities.Users;
using Domain.Helpers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Application.Features.Identity.Authentications.Commands.Register
{
    public class RegisterUserCommandHandler : BaseResponseH<PERSON><PERSON>, IRequestHandler<RegisterUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        private readonly IEmailService _emailService;
        private readonly ITokenService _tokenService;
        private readonly IEmailVerificationTokenRepository _tokenRepository;
        private readonly SecuritySettings _securitySettings;
        private readonly EmailSettings _emailSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;
        #endregion

        #region Constructor
        public RegisterUserCommandHandler(
            IIdentityServiceManager service,
            IMapper mapper,
            ILoggerManager logger,
            IEmailService emailService,
            ITokenService tokenService,
            IEmailVerificationTokenRepository tokenRepository,
            IOptions<SecuritySettings> securitySettings,
            IOptions<EmailSettings> emailSettings,
            IHttpContextAccessor httpContextAccessor)
        {
            _service = service;
            _mapper = mapper;
            _logger = logger;
            _emailService = emailService;
            _tokenService = tokenService;
            _tokenRepository = tokenRepository;
            _securitySettings = securitySettings.Value;
            _emailSettings = emailSettings.Value;
            _httpContextAccessor = httpContextAccessor;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(RegisterUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if user exists by email
                var existingUserByEmail = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (existingUserByEmail != null)
                    return BadRequest<string>("A user with this email address already exists.");

                // Check if user exists by username
                var existingUserByUsername = await _service.UserManagmentService.FindByNameAsync(request.UserName);
                if (existingUserByUsername != null)
                    return BadRequest<string>("A user with this username already exists.");

                // Create new user
                var user = new User
                {
                    FullName = request.FullName,
                    UserName = request.UserName,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber,
                    Country = request.Country,
                    Address = request.Address,
                    EmailConfirmed = false, // Will be confirmed after email verification
                    CreatedAt = DateTime.UtcNow
                };

                // Create user account
                var result = await _service.UserManagmentService.CreateAsync(user, request.Password);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return BadRequest<string>($"Failed to create user account: {errors}");
                }

                // Add user to default role
                var roleExists = await _service.AuthorizationService.IsRoleNameExist("USER");
                if (!roleExists)
                {
                    await _service.AuthorizationService.AddRoleAsync("USER", null);
                }
                await _service.UserManagmentService.AddToRoleAsync(user, "USER");

                // Generate email verification token
                var token = _tokenService.GenerateEmailVerificationToken();
                var emailVerificationToken = new EmailVerificationToken
                {
                    UserId = user.Id,
                    Token = token,
                    ExpiryDate = DateTime.UtcNow.AddHours(_securitySettings.EmailVerificationTokenExpiryHours),
                    IsUsed = false,
                    CreatedAt = DateTime.UtcNow,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent()
                };

                // Save token to database
                await _tokenRepository.CreateAsync(emailVerificationToken);
                
                // Send verification email
                var callbackUrl = $"{_emailSettings.BaseUrl}/api/Users/<USER>/Verify-Email?token={token}&email={user.Email}";
                var emailSent = await _emailService.SendEmailVerificationAsync(user.Email, user.FullName, token, callbackUrl);

                if (!emailSent)
                {
                    _logger.LogWarn($"Failed to send verification email to {user.Email}");
                    return Success<string>("User registered successfully, but verification email could not be sent. Please contact support.");
                }

                _logger.LogInfo($"User {user.Email} registered successfully. Verification email sent.");
                return Success<string>("User registered successfully. Please check your email to verify your account.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during user registration");
                return ServerError<string>("An error occurred during registration. Please try again.");
            }
        }
        #endregion

        #region Helper Methods
        private string? GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return null;

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress;
        }

        private string? GetUserAgent()
        {
            var context = _httpContextAccessor.HttpContext;
            return context?.Request.Headers["User-Agent"].FirstOrDefault();
        }
        #endregion
    }
}
