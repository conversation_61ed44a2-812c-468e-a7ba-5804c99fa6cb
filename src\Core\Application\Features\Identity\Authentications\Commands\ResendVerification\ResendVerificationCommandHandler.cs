using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Domain.Helpers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Application.Features.Identity.Authentications.Commands.ResendVerification
{
    public class ResendVerificationCommandHandler : Base<PERSON><PERSON>ponse<PERSON><PERSON><PERSON>, IRequestHandler<ResendVerificationCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly ILoggerManager _logger;
        private readonly IEmailVerificationTokenRepository _tokenRepository;
        private readonly IEmailService _emailService;
        private readonly ITokenService _tokenService;
        private readonly SecuritySettings _securitySettings;
        private readonly EmailSettings _emailSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;
        #endregion

        #region Constructor
        public ResendVerificationCommandHandler(
            IIdentityServiceManager service,
            ILoggerManager logger,
            IEmailVerificationTokenRepository tokenRepository,
            IEmailService emailService,
            ITokenService tokenService,
            IOptions<SecuritySettings> securitySettings,
            IOptions<EmailSettings> emailSettings,
            IHttpContextAccessor httpContextAccessor)
        {
            _service = service;
            _logger = logger;
            _tokenRepository = tokenRepository;
            _emailService = emailService;
            _tokenService = tokenService;
            _securitySettings = securitySettings.Value;
            _emailSettings = emailSettings.Value;
            _httpContextAccessor = httpContextAccessor;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(ResendVerificationCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Find user by email
                var user = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (user == null)
                    return BadRequest<string>("No account found with this email address.");

                // Check if user is already verified
                if (user.EmailConfirmed)
                    return BadRequest<string>("Email address is already verified.");

                // Check for existing unused token
                var existingToken = await _tokenRepository.GetByUserIdAsync(user.Id, onlyUnused: true);
                if (existingToken != null)
                {
                    // If token is still valid (not expired), don't create a new one
                    if (existingToken.ExpiryDate > DateTime.UtcNow)
                    {
                        return BadRequest<string>("A verification email was recently sent. Please check your email or wait before requesting another.");
                    }
                    
                    // Mark expired token as used
                    existingToken.IsUsed = true;
                    await _tokenRepository.UpdateAsync(existingToken);
                }

                // Generate new verification token
                var token = _tokenService.GenerateEmailVerificationToken();
                var emailVerificationToken = new EmailVerificationToken
                {
                    UserId = user.Id,
                    Token = token,
                    ExpiryDate = DateTime.UtcNow.AddHours(_securitySettings.EmailVerificationTokenExpiryHours),
                    IsUsed = false,
                    CreatedAt = DateTime.UtcNow,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = GetUserAgent()
                };

                // Save token to database
                await _tokenRepository.CreateAsync(emailVerificationToken);

                // Send verification email
                var callbackUrl = $"{_emailSettings.BaseUrl}/api/Users/<USER>/Verify-Email?token={token}&email={user.Email}";
                var emailSent = await _emailService.SendEmailVerificationAsync(user.Email, user.FullName, token, callbackUrl);

                if (!emailSent)
                {
                    _logger.LogWarn($"Failed to resend verification email to {user.Email}");
                    return ServerError<string>("Failed to send verification email. Please try again later.");
                }

                _logger.LogInfo($"Verification email resent to {user.Email}");
                return Success<string>("Verification email sent successfully. Please check your email.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while resending verification email");
                return ServerError<string>("An error occurred while sending verification email. Please try again.");
            }
        }
        #endregion

        #region Helper Methods
        private string? GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return null;

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress;
        }

        private string? GetUserAgent()
        {
            var context = _httpContextAccessor.HttpContext;
            return context?.Request.Headers["User-Agent"].FirstOrDefault();
        }
        #endregion
    }
}
