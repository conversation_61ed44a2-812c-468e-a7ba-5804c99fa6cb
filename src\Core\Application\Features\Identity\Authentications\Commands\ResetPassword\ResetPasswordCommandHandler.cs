using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Application.Base.Abstracts;
using MediatR;
using Microsoft.AspNetCore.Http;

namespace Application.Features.Identity.Authentications.Commands.ResetPassword
{
    public class ResetPasswordCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, IRequestHandler<ResetPasswordCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly ILoggerManager _logger;
        private readonly IPasswordResetTokenRepository _tokenRepository;
        private readonly IHttpContextAccessor _httpContextAccessor;
        #endregion

        #region Constructor
        public ResetPasswordCommandHandler(
            IIdentityServiceManager service,
            ILoggerManager logger,
            IPasswordResetTokenRepository tokenRepository,
            IHttpContextAccessor httpContextAccessor)
        {
            _service = service;
            _logger = logger;
            _tokenRepository = tokenRepository;
            _httpContextAccessor = httpContextAccessor;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(ResetPasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Find user by email
                var user = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (user == null)
                    return BadRequest<string>("Invalid password reset request.");

                // Find and validate token
                var token = await _tokenRepository.GetByTokenAsync(request.Token);
                if (token == null)
                    return BadRequest<string>("Invalid or expired password reset token.");

                // Check if token belongs to the user
                if (token.UserId != user.Id)
                    return BadRequest<string>("Invalid password reset request.");

                // Check if token is already used
                if (token.IsUsed)
                    return BadRequest<string>("This password reset token has already been used.");

                // Check if token is expired
                if (token.ExpiryDate <= DateTime.UtcNow)
                    return BadRequest<string>("Password reset token has expired. Please request a new password reset.");

                // Mark token as used
                token.IsUsed = true;
                await _tokenRepository.UpdateAsync(token);

                // Reset the password
                var resetToken = await _service.UserManagmentService.GeneratePasswordResetTokenAsync(user);
                var result = await _service.UserManagmentService.ResetPasswordAsync(user, resetToken, request.NewPassword);

                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError(null, $"Failed to reset password for user {user.Id}: {errors}");
                    return BadRequest<string>($"Failed to reset password: {errors}");
                }

                // Update user's last login time and security stamp
                user.LastLoginAt = DateTime.UtcNow;
                await _service.UserManagmentService.UpdateSecurityStampAsync(user);
                await _service.UserManagmentService.UpdateAsync(user);

                // Log security event
                _logger.LogInfo($"Password successfully reset for user {user.Email} from IP {GetClientIpAddress()}");

                return Success<string>("Password has been reset successfully. You can now sign in with your new password.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during password reset");
                return ServerError<string>("An error occurred during password reset. Please try again.");
            }
        }
        #endregion

        #region Helper Methods
        private string? GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return null;

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress;
        }
        #endregion
    }
}
