﻿using Domain.Helpers;
using Application.Base.Abstracts;
using Microsoft.AspNetCore.Identity;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;

namespace Application.Features.Identity.Authentications.Commands.SignIn
{
    public class SignInCommandHandler : Base<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, ICommandHandler<SignInCommand, BaseResponse<JwtAuthResponse>>
    {
        #region Fileds
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;
        private readonly IIdentityServiceManager _service;
        #endregion

        #region Constructors
        public SignInCommandHandler(UserManager<User> userManager, SignInManager<User> signInManager, IIdentityServiceManager service)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _service = service;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<JwtAuthResponse>> Handle(SignInCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _userManager.FindByNameAsync(request.UserName);
                if (user == null)
                    return NotFound<JwtAuthResponse>("User with this username not found!");

                var signInResult = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false);
                if (!signInResult.Succeeded)
                {
                    return BadRequest<JwtAuthResponse>("Password is't correct.");
                }

                var accessToken = await _service.AuthenticationService.GetJwtToken(user);
                return Success(accessToken);
            }
            catch (Exception ex)
            {
                return ServerError<JwtAuthResponse>(ex.Message);
            }
        }
        #endregion

    }
}
