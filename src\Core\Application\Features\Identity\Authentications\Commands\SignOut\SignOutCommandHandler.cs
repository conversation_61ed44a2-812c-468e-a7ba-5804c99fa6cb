using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Application.Features.Identity.Authentications.Commands.SignOut
{
    public class SignOutCommandHandler : BaseResponseHandler, IRequestHandler<SignOutCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly ITokenBlacklistService _tokenBlacklistService;
        private readonly ILoggerManager _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IIdentityServiceManager _identityService;
        #endregion

        #region Constructor
        public SignOutCommandHandler(
            ITokenBlacklistService tokenBlacklistService,
            ILoggerManager logger,
            IHttpContextAccessor httpContextAccessor,
            IIdentityServiceManager identityService)
        {
            _tokenBlacklistService = tokenBlacklistService;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _identityService = identityService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(SignOutCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var context = _httpContextAccessor.HttpContext;
                if (context?.User?.Identity?.IsAuthenticated != true)
                {
                    return BadRequest<string>("User is not authenticated.");
                }

                // Extract JWT token information
                var jti = context.User.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var expClaim = context.User.FindFirst(JwtRegisteredClaimNames.Exp)?.Value;

                if (string.IsNullOrEmpty(jti) || string.IsNullOrEmpty(userIdClaim) || string.IsNullOrEmpty(expClaim))
                {
                    return BadRequest<string>("Invalid token claims.");
                }

                if (!int.TryParse(userIdClaim, out int userId))
                {
                    return BadRequest<string>("Invalid user ID in token.");
                }

                // Convert Unix timestamp to DateTime
                var exp = DateTimeOffset.FromUnixTimeSeconds(long.Parse(expClaim)).DateTime;

                if (request.SignOutFromAllDevices)
                {
                    // Blacklist all user tokens
                    await _tokenBlacklistService.BlacklistAllUserTokensAsync(userId);
                    _logger.LogInfo($"User {userId} signed out from all devices from IP {GetClientIpAddress()}");
                    return Success<string>("Successfully signed out from all devices.");
                }
                else
                {
                    // Blacklist only current token
                    await _tokenBlacklistService.BlacklistTokenAsync(jti, exp);
                    _logger.LogInfo($"User {userId} signed out with token {jti} from IP {GetClientIpAddress()}");
                    return Success<string>("Successfully signed out.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during sign out");
                return ServerError<string>("An error occurred during sign out. Please try again.");
            }
        }
        #endregion

        #region Helper Methods
        private string? GetClientIpAddress()
        {
            var context = _httpContextAccessor.HttpContext;
            if (context == null) return null;

            var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
                ipAddress = context.Connection.RemoteIpAddress?.ToString();

            return ipAddress;
        }
        #endregion
    }
}
