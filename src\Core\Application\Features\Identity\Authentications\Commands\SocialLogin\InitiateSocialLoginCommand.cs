using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Authentications.Commands.SocialLogin
{
    public record InitiateSocialLoginCommand : ICommand<BaseResponse<SocialLoginInitiationResponse>>
    {
        public string Provider { get; set; } = null!; // Google, Facebook, Microsoft
        public string? ReturnUrl { get; set; }
    }

    public class SocialLoginInitiationResponse
    {
        public string AuthorizationUrl { get; set; } = null!;
        public string State { get; set; } = null!;
        public string Provider { get; set; } = null!;
    }
}
