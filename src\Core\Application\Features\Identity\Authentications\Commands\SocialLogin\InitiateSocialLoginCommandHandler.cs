using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Helpers;
using MediatR;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Text;

namespace Application.Features.Identity.Authentications.Commands.SocialLogin
{
    public class InitiateSocialLoginCommandHandler : BaseR<PERSON>ponse<PERSON><PERSON><PERSON>, IRequestHandler<InitiateSocialLoginCommand, BaseResponse<SocialLoginInitiationResponse>>
    {
        #region Fields
        private readonly ILoggerManager _logger;
        private readonly OAuthSettings _oauthSettings;
        private readonly ISocialAuthenticationService _socialAuthService;
        #endregion

        #region Constructor
        public InitiateSocialLoginCommandHandler(
            ILoggerManager logger,
            IOptions<OAuthSettings> oauthSettings,
            ISocialAuthenticationService socialAuthService)
        {
            _logger = logger;
            _oauthSettings = oauthSettings.Value;
            _socialAuthService = socialAuthService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<SocialLoginInitiationResponse>> Handle(InitiateSocialLoginCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check if provider is enabled
                var isProviderEnabled = request.Provider.ToLower() switch
                {
                    "google" => _oauthSettings.Google.Enabled,
                    "facebook" => _oauthSettings.Facebook.Enabled,
                    "microsoft" => _oauthSettings.Microsoft.Enabled,
                    _ => false
                };

                if (!isProviderEnabled)
                {
                    return BadRequest<SocialLoginInitiationResponse>($"{request.Provider} authentication is not enabled.");
                }

                // Generate state parameter for CSRF protection
                var state = GenerateSecureState();

                // Get authorization URL from social auth service
                var authorizationUrl = await _socialAuthService.GetAuthorizationUrlAsync(request.Provider, state, request.ReturnUrl);

                if (string.IsNullOrEmpty(authorizationUrl))
                {
                    return ServerError<SocialLoginInitiationResponse>("Failed to generate authorization URL.");
                }

                var response = new SocialLoginInitiationResponse
                {
                    AuthorizationUrl = authorizationUrl,
                    State = state,
                    Provider = request.Provider
                };

                _logger.LogInfo($"Social login initiated for provider: {request.Provider}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error initiating social login for provider: {request.Provider}");
                return ServerError<SocialLoginInitiationResponse>("An error occurred while initiating social login.");
            }
        }
        #endregion

        #region Helper Methods
        private string GenerateSecureState()
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[32];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }
        #endregion
    }
}
