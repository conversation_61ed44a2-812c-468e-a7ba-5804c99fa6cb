using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Helpers;

namespace Application.Features.Identity.Authentications.Commands.SocialLogin
{
    public record SocialLoginCallbackCommand : ICommand<BaseResponse<JwtAuthResponse>>
    {
        public string Provider { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string State { get; set; } = null!;
        public string? Error { get; set; }
        public string? ErrorDescription { get; set; }
    }
}
