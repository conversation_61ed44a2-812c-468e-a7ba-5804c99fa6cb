using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Domain.Helpers;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Application.Features.Identity.Authentications.Commands.SocialLogin
{
    public class SocialLoginCallbackCommandHandler : BaseResponseHandler, IRequestHandler<SocialLoginCallbackCommand, BaseResponse<JwtAuthResponse>>
    {
        #region Fields
        private readonly IIdentityServiceManager _identityService;
        private readonly ISocialAuthenticationService _socialAuthService;
        private readonly ILoggerManager _logger;
        private readonly UserManager<User> _userManager;
        #endregion

        #region Constructor
        public SocialLoginCallbackCommandHandler(
            IIdentityServiceManager identityService,
            ISocialAuthenticationService socialAuthService,
            ILoggerManager logger,
            UserManager<User> userManager)
        {
            _identityService = identityService;
            _socialAuthService = socialAuthService;
            _logger = logger;
            _userManager = userManager;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<JwtAuthResponse>> Handle(SocialLoginCallbackCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Check for OAuth errors
                if (!string.IsNullOrEmpty(request.Error))
                {
                    _logger.LogWarn($"OAuth error from {request.Provider}: {request.Error} - {request.ErrorDescription}");
                    return BadRequest<JwtAuthResponse>($"Authentication failed: {request.ErrorDescription ?? request.Error}");
                }

                // Get user info from social provider
                var socialUserInfo = await _socialAuthService.GetUserInfoAsync(request.Provider, request.Code, request.State);
                if (socialUserInfo == null)
                {
                    return BadRequest<JwtAuthResponse>("Failed to retrieve user information from social provider.");
                }

                // Try to find existing user by external login
                var existingUser = await _socialAuthService.FindUserByExternalLoginAsync(request.Provider, socialUserInfo.Id);

                User user;
                if (existingUser != null)
                {
                    // User exists, update external login info
                    user = existingUser;
                    await _socialAuthService.CreateOrUpdateExternalLoginAsync(user, socialUserInfo, request.Provider);
                }
                else
                {
                    // Try to find user by email
                    var userByEmail = await _userManager.FindByEmailAsync(socialUserInfo.Email);
                    if (userByEmail != null)
                    {
                        // Link existing account with social provider
                        user = userByEmail;
                        await _socialAuthService.CreateOrUpdateExternalLoginAsync(user, socialUserInfo, request.Provider);
                    }
                    else
                    {
                        // Create new user account
                        user = new User
                        {
                            UserName = socialUserInfo.Email,
                            Email = socialUserInfo.Email,
                            FullName = $"{socialUserInfo.FirstName} {socialUserInfo.LastName}".Trim(),
                            EmailConfirmed = true, // Social providers verify email
                            EmailVerifiedAt = DateTime.UtcNow,
                            CreatedAt = DateTime.UtcNow,
                            LastLoginAt = DateTime.UtcNow
                        };

                        var createResult = await _userManager.CreateAsync(user);
                        if (!createResult.Succeeded)
                        {
                            var errors = string.Join(", ", createResult.Errors.Select(e => e.Description));
                            _logger.LogError(null, $"Failed to create user from social login: {errors}");
                            return BadRequest<JwtAuthResponse>($"Failed to create user account: {errors}");
                        }

                        // Create external login record
                        await _socialAuthService.CreateOrUpdateExternalLoginAsync(user, socialUserInfo, request.Provider);
                    }
                }

                // Update last login time
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);

                // Generate JWT token
                var jwtResponse = await _identityService.AuthenticationService.GetJwtToken(user);

                _logger.LogInfo($"User {user.Email} successfully authenticated via {request.Provider}");
                return Success(jwtResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing social login callback for provider: {request.Provider}");
                return ServerError<JwtAuthResponse>("An error occurred during social authentication.");
            }
        }
        #endregion
    }
}
