using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using MediatR;

namespace Application.Features.Identity.Authentications.Commands.VerifyEmail
{
    public class VerifyEmailCommandHandler : Base<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequestHandler<VerifyEmailCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly ILoggerManager _logger;
        private readonly IEmailVerificationTokenRepository _tokenRepository;
        private readonly IEmailService _emailService;
        #endregion

        #region Constructor
        public VerifyEmailCommandHandler(
            IIdentityServiceManager service,
            ILoggerManager logger,
            IEmailVerificationTokenRepository tokenRepository,
            IEmailService emailService)
        {
            _service = service;
            _logger = logger;
            _tokenRepository = tokenRepository;
            _emailService = emailService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<string>> Handle(VerifyEmailCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Find user by email
                var user = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (user == null)
                    return BadRequest<string>("Invalid verification request.");

                // Check if user is already verified
                if (user.EmailConfirmed)
                    return BadRequest<string>("Email address is already verified.");

                // Find and validate token
                var token = await _tokenRepository.GetByTokenAsync(request.Token);
                if (token == null)
                    return BadRequest<string>("Invalid or expired verification token.");

                // Check if token belongs to the user
                if (token.UserId != user.Id)
                    return BadRequest<string>("Invalid verification request.");

                // Check if token is already used
                if (token.IsUsed)
                    return BadRequest<string>("This verification token has already been used.");

                // Check if token is expired
                if (token.ExpiryDate <= DateTime.UtcNow)
                    return BadRequest<string>("Verification token has expired. Please request a new verification email.");

                // Mark token as used
                token.IsUsed = true;
                await _tokenRepository.UpdateAsync(token);

                // Update user email confirmation
                user.EmailConfirmed = true;
                user.EmailVerifiedAt = DateTime.UtcNow;
                var updateResult = await _service.UserManagmentService.UpdateAsync(user);

                if (!updateResult.Succeeded)
                {
                    _logger.LogError(null, $"Failed to update user email confirmation for user {user.Id}");
                    return ServerError<string>("Failed to verify email. Please try again.");
                }

                // Send welcome email
                await _emailService.SendWelcomeEmailAsync(user.Email, user.FullName);

                _logger.LogInfo($"Email verified successfully for user {user.Email}");
                return Success<string>("Email verified successfully! Your account is now active.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during email verification");
                return ServerError<string>("An error occurred during email verification. Please try again.");
            }
        }
        #endregion
    }
}
