using Application.Features.Identity.Authentications.Commands.ForgotPassword;
using FluentValidation;

namespace Application.Features.Identity.Authentications.Validation
{
    public class ForgotPasswordValidator : AbstractValidator<ForgotPasswordCommand>
    {
        public ForgotPasswordValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("Invalid email format")
                .MaximumLength(256).WithMessage("Email cannot exceed 256 characters");
        }
    }
}
