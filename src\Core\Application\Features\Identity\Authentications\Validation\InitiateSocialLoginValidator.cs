using Application.Features.Identity.Authentications.Commands.SocialLogin;
using FluentValidation;

namespace Application.Features.Identity.Authentications.Validation
{
    public class InitiateSocialLoginValidator : AbstractValidator<InitiateSocialLoginCommand>
    {
        private readonly string[] _supportedProviders = { "Google", "Facebook", "Microsoft" };

        public InitiateSocialLoginValidator()
        {
            RuleFor(x => x.Provider)
                .NotEmpty().WithMessage("Provider is required")
                .Must(BeASupportedProvider).WithMessage("Provider must be one of: Google, Facebook, Microsoft");

            RuleFor(x => x.ReturnUrl)
                .Must(BeAValidUrl).WithMessage("Return URL must be a valid URL")
                .When(x => !string.IsNullOrEmpty(x.ReturnUrl));
        }

        private bool BeASupportedProvider(string provider)
        {
            return _supportedProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
        }

        private bool BeAValidUrl(string? url)
        {
            if (string.IsNullOrEmpty(url)) return true;
            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }
    }
}
