﻿using FluentValidation;
using Application.Features.Identity.Authentications.Commands.SignIn;

namespace Application.Features.Identity.Authentications.Validation
{
    public class SignInValidatior : AbstractValidator<SignInCommand>
    {

        public SignInValidatior()
        {
            ApplyValidationsRules();
        }

        #region Functions
        public void ApplyValidationsRules()
        {

            RuleFor(x => x.UserName)
                 .NotEmpty().WithMessage("Can't be empty.")
                 .NotNull().WithMessage("Can't be empty.");

            RuleFor(x => x.Password)
                .NotEmpty().WithMessage("Can't be empty.")
                .NotNull().WithMessage("Can't be empty.");
        }
        #endregion
    }
}
