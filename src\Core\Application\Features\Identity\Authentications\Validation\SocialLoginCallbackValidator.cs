using Application.Features.Identity.Authentications.Commands.SocialLogin;
using FluentValidation;

namespace Application.Features.Identity.Authentications.Validation
{
    public class SocialLoginCallbackValidator : AbstractValidator<SocialLoginCallbackCommand>
    {
        private readonly string[] _supportedProviders = { "Google", "Facebook", "Microsoft" };

        public SocialLoginCallbackValidator()
        {
            RuleFor(x => x.Provider)
                .NotEmpty().WithMessage("Provider is required")
                .Must(BeASupportedProvider).WithMessage("Provider must be one of: Google, Facebook, Microsoft");

            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("Authorization code is required")
                .When(x => string.IsNullOrEmpty(x.Error));

            RuleFor(x => x.State)
                .NotEmpty().WithMessage("State parameter is required");
        }

        private bool BeASupportedProvider(string provider)
        {
            return _supportedProviders.Contains(provider, StringComparer.OrdinalIgnoreCase);
        }
    }
}
