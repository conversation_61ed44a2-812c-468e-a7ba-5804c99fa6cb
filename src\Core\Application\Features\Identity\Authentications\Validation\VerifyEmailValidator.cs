using Application.Features.Identity.Authentications.Commands.VerifyEmail;
using FluentValidation;

namespace Application.Features.Identity.Authentications.Validation
{
    public class VerifyEmailValidator : AbstractValidator<VerifyEmailCommand>
    {
        public VerifyEmailValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("Invalid email format");

            RuleFor(x => x.Token)
                .NotEmpty().WithMessage("Verification token is required")
                .Length(10, 100).WithMessage("Invalid token format");
        }
    }
}
