using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;

namespace Application.Features.Identity.Profile.Commands.ChangeAvatar
{
    public record ChangeAvatarCommand : ICommand<BaseResponse<ChangeAvatarResponse>>
    {
        public int UserId { get; set; }
        public IFormFile AvatarFile { get; set; } = null!;
    }

    public class ChangeAvatarResponse
    {
        public string Message { get; set; } = null!;
        public string AvatarUrl { get; set; } = null!;
        public string ThumbnailUrl { get; set; } = null!;
        public DateTime UpdatedAt { get; set; }
    }
}
