using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Application.Features.Identity.Profile.Commands.ChangeAvatar
{
    public class ChangeAvatarCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, IRequestHandler<ChangeAvatarCommand, BaseResponse<ChangeAvatarResponse>>
    {
        #region Fields
        private readonly UserManager<User> _userManager;
        private readonly ILoggerManager _logger;
        private readonly IFileStorageService _fileStorageService;
        #endregion

        #region Constructor
        public ChangeAvatarCommandHandler(
            UserManager<User> userManager,
            ILoggerManager logger,
            IFileStorageService fileStorageService)
        {
            _userManager = userManager;
            _logger = logger;
            _fileStorageService = fileStorageService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<ChangeAvatarResponse>> Handle(ChangeAvatarCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarn($"User with ID {request.UserId} not found for avatar change");
                    return NotFound<ChangeAvatarResponse>("User not found.");
                }

                // Delete old avatar if exists
                if (!string.IsNullOrEmpty(user.AvatarUrl))
                {
                    try
                    {
                        await _fileStorageService.DeleteAvatarAsync(user.AvatarUrl);
                        _logger.LogInfo($"Old avatar deleted for user ID: {request.UserId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to delete old avatar for user ID: {request.UserId}");
                        // Don't fail the entire operation if old file deletion fails
                    }
                }

                // Upload new avatar
                var uploadResult = await _fileStorageService.UploadAvatarAsync(request.AvatarFile, request.UserId);
                if (!uploadResult.Success)
                {
                    _logger.LogError(null, $"Failed to upload avatar for user ID {request.UserId}: {uploadResult.ErrorMessage}");
                    return BadRequest<ChangeAvatarResponse>(uploadResult.ErrorMessage ?? "Failed to upload avatar.");
                }

                // Update user avatar URL
                user.AvatarUrl = uploadResult.FileUrl;
                user.ProfileUpdatedAt = DateTime.UtcNow;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError(null, $"Failed to update user avatar URL for user ID {request.UserId}: {errors}");
                    
                    // Clean up uploaded file if user update fails
                    try
                    {
                        await _fileStorageService.DeleteAvatarAsync(uploadResult.FileUrl!);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to clean up uploaded avatar after user update failure for user ID: {request.UserId}");
                    }

                    return BadRequest<ChangeAvatarResponse>($"Failed to update avatar: {errors}");
                }

                var response = new ChangeAvatarResponse
                {
                    Message = "Avatar updated successfully.",
                    AvatarUrl = uploadResult.FileUrl!,
                    ThumbnailUrl = uploadResult.ThumbnailUrl!,
                    UpdatedAt = user.ProfileUpdatedAt.Value
                };

                _logger.LogInfo($"Avatar updated successfully for user ID: {request.UserId}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error changing avatar for user ID: {request.UserId}");
                return ServerError<ChangeAvatarResponse>("An error occurred while changing the avatar.");
            }
        }
        #endregion
    }
}
