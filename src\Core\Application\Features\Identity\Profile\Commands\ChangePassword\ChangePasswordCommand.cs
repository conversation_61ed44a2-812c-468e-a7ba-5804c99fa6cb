using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Profile.Commands.ChangePassword
{
    public record ChangePasswordCommand : ICommand<BaseResponse<ChangePasswordResponse>>
    {
        public int UserId { get; set; }
        public string CurrentPassword { get; set; } = null!;
        public string NewPassword { get; set; } = null!;
        public string ConfirmPassword { get; set; } = null!;
        public bool InvalidateAllTokens { get; set; } = true;
    }

    public class ChangePasswordResponse
    {
        public string Message { get; set; } = null!;
        public bool TokensInvalidated { get; set; }
        public DateTime ChangedAt { get; set; }
    }
}
