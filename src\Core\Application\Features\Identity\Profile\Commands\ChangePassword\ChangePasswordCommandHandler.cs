using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Application.Features.Identity.Profile.Commands.ChangePassword
{
    public class ChangePasswordCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, IRequestHandler<ChangePasswordCommand, BaseResponse<ChangePasswordResponse>>
    {
        #region Fields
        private readonly UserManager<User> _userManager;
        private readonly ILoggerManager _logger;
        private readonly ITokenBlacklistService _tokenBlacklistService;
        #endregion

        #region Constructor
        public ChangePasswordCommandHandler(
            UserManager<User> userManager,
            ILoggerManager logger,
            ITokenBlacklistService tokenBlacklistService)
        {
            _userManager = userManager;
            _logger = logger;
            _tokenBlacklistService = tokenBlacklistService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<ChangePasswordResponse>> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarn($"User with ID {request.UserId} not found for password change");
                    return NotFound<ChangePasswordResponse>("User not found.");
                }

                // Verify current password
                var isCurrentPasswordValid = await _userManager.CheckPasswordAsync(user, request.CurrentPassword);
                if (!isCurrentPasswordValid)
                {
                    _logger.LogWarn($"Invalid current password provided for user ID: {request.UserId}");
                    return BadRequest<ChangePasswordResponse>("Current password is incorrect.");
                }

                // Change password
                var result = await _userManager.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError(null, $"Failed to change password for user ID {request.UserId}: {errors}");
                    return BadRequest<ChangePasswordResponse>($"Failed to change password: {errors}");
                }

                var changedAt = DateTime.UtcNow;
                var tokensInvalidated = false;

                // Invalidate all existing tokens if requested
                if (request.InvalidateAllTokens)
                {
                    try
                    {
                        await _tokenBlacklistService.BlacklistAllUserTokensAsync(request.UserId);
                        tokensInvalidated = true;
                        _logger.LogInfo($"All tokens invalidated for user ID: {request.UserId} after password change");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to invalidate tokens for user ID: {request.UserId}");
                        // Don't fail the entire operation if token invalidation fails
                    }
                }

                // Update security stamp to invalidate any remaining tokens
                await _userManager.UpdateSecurityStampAsync(user);

                var response = new ChangePasswordResponse
                {
                    Message = "Password changed successfully.",
                    TokensInvalidated = tokensInvalidated,
                    ChangedAt = changedAt
                };

                // Audit log for security
                _logger.LogInfo($"Password changed successfully for user ID: {request.UserId} at {changedAt}. Tokens invalidated: {tokensInvalidated}");
                
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error changing password for user ID: {request.UserId}");
                return ServerError<ChangePasswordResponse>("An error occurred while changing the password.");
            }
        }
        #endregion
    }
}
