using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Profile.Commands.UpdateProfile
{
    public record UpdateProfileCommand : ICommand<BaseResponse<UpdateProfileResponse>>
    {
        public int UserId { get; set; }
        public string FullName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? Country { get; set; }
        public string? Bio { get; set; }
        public DateTime? DateOfBirth { get; set; }
    }

    public class UpdateProfileResponse
    {
        public string Message { get; set; } = null!;
        public bool EmailChanged { get; set; }
        public bool RequiresEmailVerification { get; set; }
    }
}
