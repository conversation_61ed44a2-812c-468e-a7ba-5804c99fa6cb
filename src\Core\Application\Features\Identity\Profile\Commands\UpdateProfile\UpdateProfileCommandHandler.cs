using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Application.Features.Identity.Profile.Commands.UpdateProfile
{
    public class UpdateProfileCommandHandler : BaseRespo<PERSON><PERSON><PERSON><PERSON>, IRequestHandler<UpdateProfileCommand, BaseResponse<UpdateProfileResponse>>
    {
        #region Fields
        private readonly UserManager<User> _userManager;
        private readonly ILoggerManager _logger;
        private readonly IEmailService _emailService;
        #endregion

        #region Constructor
        public UpdateProfileCommandHandler(
            UserManager<User> userManager,
            ILoggerManager logger,
            IEmailService emailService)
        {
            _userManager = userManager;
            _logger = logger;
            _emailService = emailService;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<UpdateProfileResponse>> Handle(UpdateProfileCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarn($"User with ID {request.UserId} not found");
                    return NotFound<UpdateProfileResponse>("User not found.");
                }

                var originalEmail = user.Email;
                var emailChanged = !string.Equals(originalEmail, request.Email, StringComparison.OrdinalIgnoreCase);

                // Update user properties
                user.FullName = request.FullName;
                user.PhoneNumber = request.PhoneNumber;
                user.Address = request.Address;
                user.Country = request.Country;
                user.Bio = request.Bio;
                user.DateOfBirth = request.DateOfBirth;
                user.ProfileUpdatedAt = DateTime.UtcNow;

                // Handle email change
                if (emailChanged)
                {
                    // Check if new email is already taken
                    var existingUser = await _userManager.FindByEmailAsync(request.Email);
                    if (existingUser != null && existingUser.Id != user.Id)
                    {
                        return BadRequest<UpdateProfileResponse>("Email address is already in use.");
                    }

                    user.Email = request.Email;
                    user.UserName = request.Email; // Update username to match email
                    user.EmailConfirmed = false; // Require re-verification
                    user.EmailVerifiedAt = null;
                }

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogError(null, $"Failed to update profile for user ID {request.UserId}: {errors}");
                    return BadRequest<UpdateProfileResponse>($"Failed to update profile: {errors}");
                }

                // Send email verification if email changed
                if (emailChanged)
                {
                    try
                    {
                        var token = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                        var callbackUrl = $"https://localhost:7012/api/Users/<USER>/Verify-Email?token={Uri.EscapeDataString(token)}&email={Uri.EscapeDataString(user.Email)}";
                        
                        await _emailService.SendEmailVerificationAsync(user.Email, user.FullName, token, callbackUrl);
                        _logger.LogInfo($"Email verification sent to new email address for user ID: {request.UserId}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to send email verification for user ID: {request.UserId}");
                        // Don't fail the entire operation if email sending fails
                    }
                }

                var response = new UpdateProfileResponse
                {
                    Message = emailChanged ? 
                        "Profile updated successfully. Please check your new email address for verification." : 
                        "Profile updated successfully.",
                    EmailChanged = emailChanged,
                    RequiresEmailVerification = emailChanged
                };

                _logger.LogInfo($"Profile updated successfully for user ID: {request.UserId}. Email changed: {emailChanged}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating profile for user ID: {request.UserId}");
                return ServerError<UpdateProfileResponse>("An error occurred while updating the profile.");
            }
        }
        #endregion
    }
}
