using Application.Base.Abstracts;
using Abstraction.Base.Response;

namespace Application.Features.Identity.Profile.Queries.GetProfile
{
    public record GetProfileQuery : IQuery<BaseResponse<GetProfileResponse>>
    {
        public int UserId { get; set; }
    }

    public class GetProfileResponse
    {
        public int Id { get; set; }
        public string UserName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string FullName { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? Country { get; set; }
        public string? AvatarUrl { get; set; }
        public string? Bio { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public bool EmailConfirmed { get; set; }
        public bool PhoneNumberConfirmed { get; set; }
        public DateTime? EmailVerifiedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? LastLoginAt { get; set; }
        public DateTime? ProfileUpdatedAt { get; set; }
    }
}
