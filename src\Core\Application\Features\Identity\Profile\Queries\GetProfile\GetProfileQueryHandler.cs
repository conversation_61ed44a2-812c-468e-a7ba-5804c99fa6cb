using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Application.Features.Identity.Profile.Queries.GetProfile
{
    public class GetProfileQueryHandler : BaseR<PERSON>po<PERSON><PERSON><PERSON><PERSON>, IRequestHandler<GetProfileQuery, BaseResponse<GetProfileResponse>>
    {
        #region Fields
        private readonly UserManager<User> _userManager;
        private readonly ILoggerManager _logger;
        #endregion

        #region Constructor
        public GetProfileQueryHandler(
            UserManager<User> userManager,
            ILoggerManager logger)
        {
            _userManager = userManager;
            _logger = logger;
        }
        #endregion

        #region Handler
        public async Task<BaseResponse<GetProfileResponse>> Handle(GetProfileQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(request.UserId.ToString());
                if (user == null)
                {
                    _logger.LogWarn($"User with ID {request.UserId} not found");
                    return NotFound<GetProfileResponse>("User not found.");
                }

                var response = new GetProfileResponse
                {
                    Id = user.Id,
                    UserName = user.UserName!,
                    Email = user.Email!,
                    FullName = user.FullName,
                    PhoneNumber = user.PhoneNumber,
                    Address = user.Address,
                    Country = user.Country,
                    AvatarUrl = user.AvatarUrl,
                    Bio = user.Bio,
                    DateOfBirth = user.DateOfBirth,
                    EmailConfirmed = user.EmailConfirmed,
                    PhoneNumberConfirmed = user.PhoneNumberConfirmed,
                    EmailVerifiedAt = user.EmailVerifiedAt,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt,
                    ProfileUpdatedAt = user.ProfileUpdatedAt
                };

                _logger.LogInfo($"Profile retrieved successfully for user ID: {request.UserId}");
                return Success(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving profile for user ID: {request.UserId}");
                return ServerError<GetProfileResponse>("An error occurred while retrieving the profile.");
            }
        }
        #endregion
    }
}
