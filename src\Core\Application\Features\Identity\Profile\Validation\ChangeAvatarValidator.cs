using Application.Features.Identity.Profile.Commands.ChangeAvatar;
using FluentValidation;

namespace Application.Features.Identity.Profile.Validation
{
    public class ChangeAvatarValidator : AbstractValidator<ChangeAvatarCommand>
    {
        private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".webp" };
        private readonly string[] _allowedMimeTypes = { "image/jpeg", "image/png", "image/webp" };
        private readonly long _maxFileSize = 5 * 1024 * 1024; // 5MB

        public ChangeAvatarValidator()
        {
            RuleFor(x => x.UserId)
                .GreaterThan(0).WithMessage("User ID must be greater than 0");

            RuleFor(x => x.AvatarFile)
                .NotNull().WithMessage("Avatar file is required")
                .Must(BeAValidFile).WithMessage("Avatar file is required and must be a valid file")
                .Must(HaveValidExtension).WithMessage($"Avatar file must have one of the following extensions: {string.Join(", ", _allowedExtensions)}")
                .Must(HaveValidMimeType).WithMessage($"Avatar file must be one of the following types: {string.Join(", ", _allowedMimeTypes)}")
                .Must(BeWithinSizeLimit).WithMessage($"Avatar file size must not exceed {_maxFileSize / (1024 * 1024)}MB");
        }

        private bool BeAValidFile(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            return file != null && file.Length > 0;
        }

        private bool HaveValidExtension(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            if (file == null) return false;
            
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return _allowedExtensions.Contains(extension);
        }

        private bool HaveValidMimeType(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            if (file == null) return false;
            
            return _allowedMimeTypes.Contains(file.ContentType.ToLowerInvariant());
        }

        private bool BeWithinSizeLimit(Microsoft.AspNetCore.Http.IFormFile? file)
        {
            if (file == null) return false;
            
            return file.Length <= _maxFileSize;
        }
    }
}
