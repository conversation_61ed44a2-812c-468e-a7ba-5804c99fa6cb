using Application.Features.Identity.Profile.Queries.GetProfile;
using FluentValidation;

namespace Application.Features.Identity.Profile.Validation
{
    public class GetProfileValidator : AbstractValidator<GetProfileQuery>
    {
        public GetProfileValidator()
        {
            RuleFor(x => x.UserId)
                .GreaterThan(0).WithMessage("User ID must be greater than 0");
        }
    }
}
