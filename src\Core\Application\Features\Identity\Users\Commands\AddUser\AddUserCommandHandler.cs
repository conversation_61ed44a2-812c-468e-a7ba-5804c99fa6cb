﻿using AutoMapper;
using Application.Base.Abstracts;
using Domain.Entities.Users;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;

namespace Application.Features.Identity.Users.Commands.AddUser
{
    public class AddUserCommandHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ICommandHandler<AddUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public AddUserCommandHandler(IIdentityServiceManager service, IMapper mapper)
        {
            _mapper = mapper;
            _service = service;
        }
        #endregion

        #region Functions

        public async Task<BaseResponse<string>> Handle(AddUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                //write this logic if program is needed.
                var IsUserExistByEmail = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (IsUserExistByEmail != null)
                    return BadRequest<string>("this email already before used.");

                //write this logic if needed.
                var IsUserExistByUserName = await _service.UserManagmentService.FindByNameAsync(request.UserName);
                if (IsUserExistByUserName != null) return BadRequest<string>("this username already before used.");

                var user = _mapper.Map<User>(request);
                var result = await _service.UserManagmentService.CreateAsync(user, request.Password);
                if (!result.Succeeded)
                {
                    string errorMessage = "Errors occurred while creating the user: ";
                    foreach (var error in result.Errors)
                    {
                        errorMessage += "\n" + $"{error.Description}";
                    }
                    return new BaseResponse<string>(errorMessage);
                }
                var _roleExist = await _service.AuthorizationService.IsRoleNameExist("USER");
                if (!_roleExist)
                {
                    await _service.AuthorizationService.AddRoleAsync("USER",null);
                }
                //Add role for this user
                await _service.UserManagmentService.AddToRoleAsync(user, "USER");

                return Created("User Added Successfully.");
            }
            catch (Exception ex)
            {
                return ServerError<string>(ex.Message);
            }
        }
        #endregion
    }
}
