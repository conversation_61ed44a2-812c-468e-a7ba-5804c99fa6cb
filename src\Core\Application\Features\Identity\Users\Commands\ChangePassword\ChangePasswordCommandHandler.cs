﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;

namespace Application.Features.Identity.Users.Commands.ChangePassword
{
    public class ChangePasswordCommandHandler : BaseResponseHandler, ICommandHandler<ChangePasswordCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public ChangePasswordCommandHandler(IIdentityServiceManager service, IMapper mapper)
        {
            _mapper = mapper;
            _service = service;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(ChangePasswordCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var user = await _service.UserManagmentService.FindByIdAsync(request.Id.ToString());
                if (user == null)
                    return NotFound<string>($"User with Id: {request.Id} not found!");


                // there are many ways for change password.
                // (way 1)  => Find , Delete , Add
                //var IsHasPassword = await _userManager.HasPasswordAsync(user);
                //await _userManager.RemovePasswordAsync(user);
                //await _userManager.AddPasswordAsync(user, request.NewPassword);


                var result = await _service.UserManagmentService.ChangePasswordAsync(user, request.CurrentPassword, request.NewPassword);
                if (!result.Succeeded)
                    return BadRequest<string>(result.Errors.FirstOrDefault()?.Description);

                return Success("Change Password Operation Successfully.");
            }
            catch (Exception ex)
            {
                return ServerError<string>(ex.Message);
            }
        }
        #endregion
    }
}
