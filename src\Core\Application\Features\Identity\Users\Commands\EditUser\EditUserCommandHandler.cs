﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Identity;


namespace Application.Features.Identity.Users.Commands.EditUser
{
    public class EditUserCommandHandler : BaseResponseHandler, ICommandHandler<EditUserCommand, BaseResponse<string>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public EditUserCommandHandler(IIdentityServiceManager service, IMapper mapper)
        {
            _mapper = mapper;
            _service = service;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<string>> Handle(EditUserCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var oldUser = await _service.UserManagmentService.FindByIdAsync(request.Id.ToString());
                if (oldUser == null)
                    return NotFound<string>($"User with id: {request.Id} not found!");

                var IsUserExistByEmail = await _service.UserManagmentService.FindByEmailAsync(request.Email);
                if (IsUserExistByEmail != null && IsUserExistByEmail.Id != request.Id)
                    return BadRequest<string>("this email already before used.");

                var IsUserExistByUserName = await _service.UserManagmentService.FindByNameAsync(request.UserName);
                if (IsUserExistByUserName != null && IsUserExistByUserName.UserName != request.UserName)
                    return BadRequest<string>("this username already before used.");

                // استخدم الـ Mapper لتحديث الحقول فقط على الكائن القديم
                var userMapper = _mapper.Map(request, oldUser);

                //في الحالة ده هو ببنشالك كائن جديد من user مما يسبب بعض المشاكل 
                //var UserMapper = _mapper.Map<User>(request);

                var result = await _service.UserManagmentService.UpdateAsync(userMapper);
                if (!result.Succeeded)
                    return BadRequest<string>(result.Errors.FirstOrDefault()?.Description);

                return Success("Updated User is Successfully");
            }
            catch (Exception ex)
            {
                return ServerError<string>(ex.Message);
            }
        }
        #endregion
    }
}
