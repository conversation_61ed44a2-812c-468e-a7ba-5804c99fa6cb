﻿using AutoMapper;
using Application.Features.Identity.Users.Queries.Responses;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Contracts.Logger;
using Abstraction.Common.Wappers;
using Abstraction.Contracts.Identity;

namespace Application.Features.Identity.Users.Queries.List
{

    public class ListQueryHandler : BaseResponse<PERSON><PERSON><PERSON>, IQueryHandler<ListQuery, PaginatedResult<GetUserListResponse>>
    {
        #region Fields
        private readonly IIdentityServiceManager _service;
        private readonly IMapper _mapper;
        private readonly ILoggerManager _logger;
        #endregion

        #region Constructors
        public ListQueryHandler(IIdentityServiceManager service, IMapper mapper, ILoggerManager logger)
        {
            _mapper = mapper;
            _service = service;
            _logger = logger;
        }
        #endregion

        #region Functions
        public async Task<PaginatedResult<GetUserListResponse>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var users = _service.UserManagmentService.Users().AsQueryable();
                if (!users.Any())
                {
                    return new PaginatedResult<GetUserListResponse>(new());
                }

                var paginatedList = await _mapper.ProjectTo<GetUserListResponse>(users).ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);
                return paginatedList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetUserPaginatedListQuery");
                throw;
            }
        }



        #endregion

    }
}
