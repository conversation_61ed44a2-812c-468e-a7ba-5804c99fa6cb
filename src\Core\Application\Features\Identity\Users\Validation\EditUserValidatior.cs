﻿using FluentValidation;
using Application.Features.Identity.Users.Commands.EditUser;

namespace Application.Features.Identity.Users.Validation
{
    public class EditUserValidatior : AbstractValidator<EditUserCommand>
    {

        #region Constructors
        public EditUserValidatior()
        {
            ApplyValidationsRules();
        }
        #endregion

        #region Functions
        public void ApplyValidationsRules()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Can't be empty.")
                .NotNull().WithMessage("Can't be blank.");

            RuleFor(x => x.FullName)
                .NotEmpty().WithMessage("Can't be empty.")
                .NotNull().WithMessage("Can't be blank.")
                .MaximumLength(200);

            RuleFor(x => x.UserName)
               .NotEmpty().WithMessage("Can't be empty.")
               .NotNull().WithMessage("Can't be blank.")
               .MaximumLength(100);

            RuleFor(x => x.Email)
               .NotEmpty().WithMessage("Can't be empty.")
               .NotNull().WithMessage("Can't be blank.")
               .MaximumLength(100);


        }
        #endregion

    }
}
