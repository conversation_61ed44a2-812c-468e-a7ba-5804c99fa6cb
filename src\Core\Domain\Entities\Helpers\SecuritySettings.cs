namespace Domain.Helpers
{
    public record SecuritySettings
    {
        public int EmailVerificationTokenExpiryHours { get; set; } = 24;
        public int PasswordResetTokenExpiryHours { get; set; } = 1;
        public int MaxPasswordResetAttemptsPerHour { get; set; } = 3;
        public int MaxRegistrationAttemptsPerHour { get; set; } = 5;
        public int MaxEmailVerificationAttemptsPerHour { get; set; } = 3;
        public bool RequireEmailConfirmation { get; set; } = true;
        public int TokenLength { get; set; } = 32;
    }
}
