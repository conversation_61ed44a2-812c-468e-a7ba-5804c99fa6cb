using Domain.Entities.Base;

namespace Domain.Entities.Users
{
    public class PasswordResetToken : BaseEntity
    {
        public int UserId { get; set; }
        public string Token { get; set; } = null!;
        public DateTime ExpiryDate { get; set; }
        public bool IsUsed { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        
        // Navigation property
        public User User { get; set; } = null!;
    }
}
