using Domain.Entities.Base;

namespace Domain.Entities.Users
{
    public class SocialProvider : BaseEntity
    {
        public string Name { get; set; } = null!; // Google, Facebook, Microsoft, etc.
        public string DisplayName { get; set; } = null!;
        public string ClientId { get; set; } = null!;
        public string ClientSecret { get; set; } = null!;
        public string AuthorizationEndpoint { get; set; } = null!;
        public string TokenEndpoint { get; set; } = null!;
        public string UserInfoEndpoint { get; set; } = null!;
        public string Scope { get; set; } = null!;
        public bool IsEnabled { get; set; } = true;
        public string? IconUrl { get; set; }
        public int SortOrder { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<UserExternalLogin> UserExternalLogins { get; set; } = new List<UserExternalLogin>();
    }
}
