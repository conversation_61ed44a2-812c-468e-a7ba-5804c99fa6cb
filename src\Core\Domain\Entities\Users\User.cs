﻿using Microsoft.AspNetCore.Identity;

namespace Domain.Entities.Users
{
    public class User : IdentityUser<int>
    {
        public string FullName { get; set; } = null!;
        public string? Address { get; set; }
        public string? Country { get; set; }
        public string? AvatarUrl { get; set; }
        public string? Bio { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public DateTime? EmailVerifiedAt { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastLoginAt { get; set; }
        public DateTime? ProfileUpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<UserRefreshToken> UserRefreshTokens { get; set; } = null!;
        public virtual ICollection<EmailVerificationToken> EmailVerificationTokens { get; set; } = null!;
        public virtual ICollection<PasswordResetToken> PasswordResetTokens { get; set; } = null!;
        public virtual ICollection<UserExternalLogin> ExternalLogins { get; set; } = null!;
    }
}
