using Domain.Entities.Base;

namespace Domain.Entities.Users
{
    public class UserExternalLogin : BaseEntity
    {
        public int UserId { get; set; }
        public int SocialProviderId { get; set; }
        public string ProviderKey { get; set; } = null!; // External user ID from provider
        public string? ProviderDisplayName { get; set; }
        public string? Email { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? ProfilePictureUrl { get; set; }
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? TokenExpiresAt { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastLoginAt { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual SocialProvider SocialProvider { get; set; } = null!;
    }
}
