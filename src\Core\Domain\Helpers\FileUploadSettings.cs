namespace Domain.Helpers
{
    public class FileUploadSettings
    {
        public AvatarUploadSettings Avatar { get; set; } = new();
    }

    public class AvatarUploadSettings
    {
        public long MaxFileSizeBytes { get; set; } = 5 * 1024 * 1024; // 5MB
        public string[] AllowedFileTypes { get; set; } = { ".jpg", ".jpeg", ".png", ".webp" };
        public string[] AllowedMimeTypes { get; set; } = { "image/jpeg", "image/png", "image/webp" };
        public int MaxWidthPixels { get; set; } = 800;
        public int MaxHeightPixels { get; set; } = 800;
        public int ThumbnailSize { get; set; } = 150;
        public string UploadPath { get; set; } = "uploads/avatars";
        public string BaseUrl { get; set; } = "/uploads/avatars";
    }
}
