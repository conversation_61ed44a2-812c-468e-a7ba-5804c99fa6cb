namespace Domain.Helpers
{
    public class OAuthSettings
    {
        public GoogleOAuthSettings Google { get; set; } = new();
        public FacebookOAuthSettings Facebook { get; set; } = new();
        public MicrosoftOAuthSettings Microsoft { get; set; } = new();
    }

    public class GoogleOAuthSettings
    {
        public bool Enabled { get; set; } = false;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string RedirectUri { get; set; } = string.Empty;
        public string Scope { get; set; } = "openid profile email";
    }

    public class FacebookOAuthSettings
    {
        public bool Enabled { get; set; } = false;
        public string AppId { get; set; } = string.Empty;
        public string AppSecret { get; set; } = string.Empty;
        public string RedirectUri { get; set; } = string.Empty;
        public string Scope { get; set; } = "email,public_profile";
    }

    public class MicrosoftOAuthSettings
    {
        public bool Enabled { get; set; } = false;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string RedirectUri { get; set; } = string.Empty;
        public string Scope { get; set; } = "openid profile email";
        public string TenantId { get; set; } = "common";
    }
}
