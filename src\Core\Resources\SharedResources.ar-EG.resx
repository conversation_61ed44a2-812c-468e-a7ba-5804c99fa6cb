﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="EmptyIdValidation" xml:space="preserve">
    <value>المعرف مطلوب</value>
  </data>
  <data name="EmptyNameValidation" xml:space="preserve">
    <value>الاسم مطلوب</value>
  </data>
  <data name="ExistICNumberValidation" xml:space="preserve">
    <value>رقم العميل موجود من قبل</value>
  </data>
  <data name="HasAcceptedTerms" xml:space="preserve">
    <value>يرجى قبول الشروط </value>
  </data>
  <data name="MaximumDigitsPINCodeValidation" xml:space="preserve">
    <value>الرقم السرى لا يجب ان يزيد عن 6 ارقام فقط</value>
  </data>
  <data name="MinimumDigitsPINCodeValidation" xml:space="preserve">
    <value>الرقم السرى لا يجب ان يقل عن  6 ارقام فقط</value>
  </data>
  <data name="EmptyCustomerNameValidtion" xml:space="preserve">
    <value>اسم العميل مطلوب</value>
  </data>
  <data name="EmptyCustomerPhoneValidation" xml:space="preserve">
    <value>رقم الهاتف مطلوب</value>
  </data>
  <data name="ExistCustomerPhoneValidation" xml:space="preserve">
    <value>رقم الهاتف موجود من قبل</value>
  </data>
  <data name="EmptyCustomerEmailValidation" xml:space="preserve">
    <value>البريد الإلكترونى مطلوب</value>
  </data>
  <data name="ExistCustomerEmailValidation" xml:space="preserve">
    <value>البريد الإلكترونى موجود من قبل</value>
  </data>
  <data name="EmptyTOTPValidation" xml:space="preserve">
    <value>الرقم مطلوب</value>
  </data>
  <data name="MaximumDigitsTOTPValidation" xml:space="preserve">
    <value>الرقم لا يجب ان يزيد عن 4 ارقام فقط</value>
  </data>
  <data name="MinimumDigitsTOTPValidation" xml:space="preserve">
    <value>الرقم لا يجب ان يقل عن 4 ارقام فقط</value>
  </data>
  <data name="NotValidOrExpiredTOTPValidation" xml:space="preserve">
    <value>الرقم خاطئ او انتهت صلاحيته</value>
  </data>
  <data name="NotValidPINCodeValidation" xml:space="preserve">
    <value>الرقم السرى خاطئ</value>
  </data>
  <data name="CustomerCreationFailed" xml:space="preserve">
    <value>لم تتم إضافة العميل</value>
  </data>
  <data name="PhoneTOTPIs" xml:space="preserve">
    <value>الرقم المتغير هو :</value>
  </data>
  <data name="TOTPExpireAfter" xml:space="preserve">
    <value>وستنتهى صلاحيته بعد 120 ثانية</value>
  </data>
  <data name="PINCodeUpdated" xml:space="preserve">
    <value>تم تغيير الرقم السرى بنجاح</value>
  </data>
  <data name="PINCodeCreationFailed" xml:space="preserve">
    <value>فشل تغيير الرقم السرى</value>
  </data>
  <data name="BiometricLoginEnabledSucessfully" xml:space="preserve">
    <value>تم تفعيل الدخول بالبصمة</value>
  </data>
  <data name="BiometricLoginEnabledFailed" xml:space="preserve">
    <value>فشل تفعيل الدخول بالبصمة</value>
  </data>
  <data name="TermsAcceptedSucessfully" xml:space="preserve">
    <value>تم قبول الشروط</value>
  </data>
  <data name="TermsAcceptedFailed" xml:space="preserve">
    <value>فشل قبول الشروط</value>
  </data>
  <data name="EmailVerirfiedSucessfully" xml:space="preserve">
    <value>تم التأكد من البريد الإلكتروني</value>
  </data>
  <data name="EmailVerificationFailed" xml:space="preserve">
    <value>فشل التأكد من البريد الإلكتروني</value>
  </data>
  <data name="PhoneVerifiedAndEmailTOTPIs" xml:space="preserve">
    <value>تم التأكد من رقم الهاتف و كود تفعيل البريد الإلكتروني هو : </value>
  </data>
  <data name="PhoneVerificationFailed" xml:space="preserve">
    <value>فشل التأكد من البريد الهاتف</value>
  </data>
  <data name="PINCodeVerirfiedSucessfully" xml:space="preserve">
    <value>تم التأكد من الرقم السري</value>
  </data>
  <data name="PINCodeVerificationFailed" xml:space="preserve">
    <value>فشل التأكد من الرقم السري</value>
  </data>
  <data name="MaximumCharsCustomerNameValidtion" xml:space="preserve">
    <value>لا يجب ان يزيد طول الاسم عن 50 حرف</value>
  </data>
  <data name="ValidEmailValidation" xml:space="preserve">
    <value>يرجى إضافة بريد إلكتروني صالح</value>
  </data>
  <data name="TheCustomerWithICNumber" xml:space="preserve">
    <value>العميل برقم : </value>
  </data>
  <data name="DoesntExist" xml:space="preserve">
    <value>غير موجود</value>
  </data>
</root>