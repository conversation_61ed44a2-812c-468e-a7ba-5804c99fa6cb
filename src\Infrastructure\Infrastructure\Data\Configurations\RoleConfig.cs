﻿
using Abstraction.Constants;
using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;



namespace Infrastructure.Data.Configurations
{
    public static class RoleConfig
    {
        public static async Task SeedAsync(RoleManager<Role> roleManager)
        {
            await roleManager.CreateAsync(new Role() { Name = Roles.SuperAdmin.ToString().ToLower() });
            await roleManager.CreateAsync(new Role() { Name = Roles.Admin.ToString().ToLower() });
            await roleManager.CreateAsync(new Role() { Name = Roles.Basic.ToString().ToLower() });
        }
    }
}