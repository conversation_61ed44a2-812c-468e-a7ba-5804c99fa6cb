﻿using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Data.Configurations
{
    public static class Seed
    {
        public static async Task SeedAsync(IServiceProvider serviceProvider)
        {
            var userManager = serviceProvider.GetRequiredService<UserManager<User>>();
            var roleManager = serviceProvider.GetRequiredService<RoleManager<Role>>();
            await RoleConfig.SeedAsync(roleManager);
            await UserConfig.SeedBasicUserAsync(userManager, roleManager);
            await UserConfig.SeedSuperAdminAsync(userManager, roleManager);
        }
    }
}
