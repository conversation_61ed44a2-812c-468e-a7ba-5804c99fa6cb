﻿using Abstraction.Constants;
using Domain.Entities.Users;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;

namespace Infrastructure.Data.Configurations
{
    public static class UserConfig
    {
        public static async Task SeedBasicUserAsync(UserManager<User> userManager, RoleManager<Role> roleManager)
        {
            //Seed Default User
            var defaultUser = new User
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true,
                PhoneNumberConfirmed = true,
                FullName = "Basic User"
            };
            if (userManager.Users.All(u => u.Id != defaultUser.Id))
            {
                var user = await userManager.FindByEmailAsync(defaultUser.Email);
                if (user == null)
                {
                    await userManager.CreateAsync(defaultUser, "123Pa$$word!");
                    await userManager.AddToRoleAsync(defaultUser, Roles.Basic.ToString());
                }
            }
        }

        public static async Task SeedSuperAdminAsync(UserManager<User> userManager, RoleManager<Role> roleManager)
        {
            //Seed Default User
            var defaultUser = new User
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmailConfirmed = true,
                PhoneNumberConfirmed = true,
                FullName = "Admin User"
            };
            if (userManager.Users.All(u => u.Id != defaultUser.Id))
            {
                var user = await userManager.FindByEmailAsync(defaultUser.Email);
                if (user == null)
                {
                    await userManager.CreateAsync(defaultUser, "123Pa$$word!");
                    await userManager.AddToRoleAsync(defaultUser, Roles.Basic.ToString());
                    await userManager.AddToRoleAsync(defaultUser, Roles.Admin.ToString());
                    await userManager.AddToRoleAsync(defaultUser, Roles.SuperAdmin.ToString());
                }
                await roleManager.SeedClaimsForSuperAdmin();
            }
        }

        private async static Task SeedClaimsForSuperAdmin(this RoleManager<Role> roleManager)
        {
            var adminRole = await roleManager.FindByNameAsync("SuperAdmin");
            foreach (var module in Claims.GenerateModules())
            {
                await roleManager.AddPermissionClaim(adminRole, module);
            }
        }
        public static async Task AddPermissionClaim(this RoleManager<Role> roleManager, Role role, string module)
        {
            var allClaims = await roleManager.GetClaimsAsync(role);
            var allPermissions = Claims.GeneratePermissions(module);
            foreach (var permission in allPermissions)
            {
                if (!allClaims.Any(a => a.Type == CustomClaimTypes.Permission && a.Value == permission))
                {
                    await roleManager.AddClaimAsync(role, new Claim(CustomClaimTypes.Permission, permission));
                }
            }
        }
    }
}