﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore" />

		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
		<PackageReference Include="NLog" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
			
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
		<PackageReference Include="SixLabors.ImageSharp" />
		<PackageReference Include="Swashbuckle.AspNetCore" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\Core\Application\Application.csproj" />
	  <ProjectReference Include="..\..\Core\Domain\Domain.csproj" />
	</ItemGroup>

</Project>
