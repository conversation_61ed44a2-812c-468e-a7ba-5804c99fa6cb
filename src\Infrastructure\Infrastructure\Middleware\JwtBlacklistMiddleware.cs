using Abstraction.Contracts.Service;
using Microsoft.AspNetCore.Http;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Infrastructure.Middleware
{
    public class JwtBlacklistMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ITokenBlacklistService _tokenBlacklistService;

        public JwtBlacklistMiddleware(RequestDelegate next, ITokenBlacklistService tokenBlacklistService)
        {
            _next = next;
            _tokenBlacklistService = tokenBlacklistService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Skip middleware for non-authenticated requests
            if (context.User?.Identity?.IsAuthenticated != true)
            {
                await _next(context);
                return;
            }

            // Extract JTI from token
            var jti = context.User.FindFirst(JwtRegisteredClaimNames.Jti)?.Value;
            if (!string.IsNullOrEmpty(jti))
            {
                // Check if token is blacklisted
                var isBlacklisted = await _tokenBlacklistService.IsTokenBlacklistedAsync(jti);
                if (isBlacklisted)
                {
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsync("Token has been revoked");
                    return;
                }

                // Additional check for user-specific token validation
                var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (int.TryParse(userIdClaim, out int userId))
                {
                    var isValidUserToken = await _tokenBlacklistService.IsUserTokenValidAsync(userId, jti);
                    if (!isValidUserToken)
                    {
                        context.Response.StatusCode = 401;
                        await context.Response.WriteAsync("Token is no longer valid for this user");
                        return;
                    }
                }
            }

            await _next(context);
        }
    }
}
