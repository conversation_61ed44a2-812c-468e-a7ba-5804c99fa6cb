using Abstraction.Contracts.Repository;
using Domain.Entities.Users;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository
{
    public class EmailVerificationTokenRepository : IEmailVerificationTokenRepository
    {
        private readonly AppDbContext _context;

        public EmailVerificationTokenRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<EmailVerificationToken?> GetByTokenAsync(string token)
        {
            return await _context.EmailVerificationTokens
                .Include(t => t.User)
                .FirstOrDefaultAsync(t => t.Token == token);
        }

        public async Task<EmailVerificationToken?> GetByUserIdAsync(int userId, bool onlyUnused = true)
        {
            var query = _context.EmailVerificationTokens
                .Where(t => t.UserId == userId);

            if (onlyUnused)
                query = query.Where(t => !t.IsUsed && t.ExpiryDate > DateTime.UtcNow);

            return await query
                .OrderByDescending(t => t.CreatedAt)
                .FirstOrDefaultAsync();
        }

        public async Task<EmailVerificationToken> CreateAsync(EmailVerificationToken token)
        {
            _context.EmailVerificationTokens.Add(token);
            await _context.SaveChangesAsync();
            return token;
        }

        public async Task UpdateAsync(EmailVerificationToken token)
        {
            _context.EmailVerificationTokens.Update(token);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> IsTokenValidAsync(string token)
        {
            return await _context.EmailVerificationTokens
                .AnyAsync(t => t.Token == token && 
                              !t.IsUsed && 
                              t.ExpiryDate > DateTime.UtcNow);
        }

        public async Task<List<EmailVerificationToken>> GetExpiredTokensAsync()
        {
            return await _context.EmailVerificationTokens
                .Where(t => t.ExpiryDate <= DateTime.UtcNow)
                .ToListAsync();
        }

        public async Task DeleteExpiredTokensAsync()
        {
            var expiredTokens = await GetExpiredTokensAsync();
            if (expiredTokens.Any())
            {
                _context.EmailVerificationTokens.RemoveRange(expiredTokens);
                await _context.SaveChangesAsync();
            }
        }
    }
}
