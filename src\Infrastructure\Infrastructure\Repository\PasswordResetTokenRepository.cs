using Abstraction.Contracts.Repository;
using Domain.Entities.Users;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Repository
{
    public class PasswordResetTokenRepository : IPasswordResetTokenRepository
    {
        private readonly AppDbContext _context;

        public PasswordResetTokenRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<PasswordResetToken?> GetByTokenAsync(string token)
        {
            return await _context.PasswordResetTokens
                .Include(t => t.User)
                .FirstOrDefaultAsync(t => t.Token == token);
        }

        public async Task<PasswordResetToken?> GetByUserIdAsync(int userId, bool onlyUnused = true)
        {
            var query = _context.PasswordResetTokens
                .Where(t => t.UserId == userId);

            if (onlyUnused)
                query = query.Where(t => !t.IsUsed && t.ExpiryDate > DateTime.UtcNow);

            return await query
                .OrderByDescending(t => t.CreatedAt)
                .FirstOrDefaultAsync();
        }

        public async Task<PasswordResetToken> CreateAsync(PasswordResetToken token)
        {
            _context.PasswordResetTokens.Add(token);
            await _context.SaveChangesAsync();
            return token;
        }

        public async Task UpdateAsync(PasswordResetToken token)
        {
            _context.PasswordResetTokens.Update(token);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> IsTokenValidAsync(string token)
        {
            return await _context.PasswordResetTokens
                .AnyAsync(t => t.Token == token && 
                              !t.IsUsed && 
                              t.ExpiryDate > DateTime.UtcNow);
        }

        public async Task<List<PasswordResetToken>> GetExpiredTokensAsync()
        {
            return await _context.PasswordResetTokens
                .Where(t => t.ExpiryDate <= DateTime.UtcNow)
                .ToListAsync();
        }

        public async Task DeleteExpiredTokensAsync()
        {
            var expiredTokens = await GetExpiredTokensAsync();
            if (expiredTokens.Any())
            {
                _context.PasswordResetTokens.RemoveRange(expiredTokens);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetRecentAttemptsCountAsync(string email, TimeSpan timeSpan)
        {
            var cutoffTime = DateTime.UtcNow.Subtract(timeSpan);
            
            return await _context.PasswordResetTokens
                .Include(t => t.User)
                .Where(t => t.User.Email == email && t.CreatedAt >= cutoffTime)
                .CountAsync();
        }
    }
}
