﻿using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Repository.Products;
using Infrastructure.Data;
using Repository.Catalog;
using Repository;

namespace Infrastructure.Repository
{
    public sealed class ProductRepositoryManager : IRepositoryManager
    {
        private readonly AppDbContext _repositoryContext;
        private readonly Lazy<ICategoryRepository> _iCategpryRepository;
        public ProductRepositoryManager(AppDbContext repositoryContext)
        {
            _repositoryContext = repositoryContext;
            _iCategpryRepository = new Lazy<ICategoryRepository>(() => new CategoryRepository(repositoryContext));
        }
        public ICategoryRepository Categories => _iCategpryRepository.Value;
    }
}
