﻿using Domain.Entities.Products;
using AutoMapper;
using Abstraction.Contracts.Service.Catalog;
using Abstraction.Contracts.Repository;
using Infrastructure.Service;

namespace Infrastructure.Onion.Service.Catalog
{
    public class DemoEntityService : BaseService<DemoEntity>, IDemoEntityService
    {
        public DemoEntityService(IGenericRepository repository, IMapper mapper) : base(repository, mapper)
        {
            _repository = repository;
        }
      
    }
}
