﻿using Domain.Entities.Products;
using AutoMapper;
using Abstraction.Contracts.Service.Catalog;
using Abstraction.Contracts.Repository;


namespace Infrastructure.Service.Catalog
{
    public class ProductService : BaseService<Product>, IProductService
    {
        public ProductService(IGenericRepository repository, IMapper mapper) : base(repository, mapper)
        {
            _repository = repository;
        }
    }
}
