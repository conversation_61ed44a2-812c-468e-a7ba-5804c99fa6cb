using System.Net;
using System.Net.Mail;
using System.Text;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Domain.Helpers;
using Microsoft.Extensions.Options;

namespace Infrastructure.Service
{
    public class EmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILoggerManager _logger;

        public EmailService(IOptions<EmailSettings> emailSettings, ILoggerManager logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task<bool> SendEmailVerificationAsync(string email, string fullName, string token, string callbackUrl)
        {
            try
            {
                var subject = "Verify Your Email Address";
                var body = GetEmailVerificationTemplate(fullName, callbackUrl);
                
                return await SendEmailAsync(email, subject, body);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email verification to {email}");
                return false;
            }
        }

        public async Task<bool> SendPasswordResetAsync(string email, string fullName, string token, string callbackUrl)
        {
            try
            {
                var subject = "Reset Your Password";
                var body = GetPasswordResetTemplate(fullName, callbackUrl);
                
                return await SendEmailAsync(email, subject, body);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send password reset email to {email}");
                return false;
            }
        }

        public async Task<bool> SendWelcomeEmailAsync(string email, string fullName)
        {
            try
            {
                var subject = "Welcome to Our Platform!";
                var body = GetWelcomeTemplate(fullName);
                
                return await SendEmailAsync(email, subject, body);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send welcome email to {email}");
                return false;
            }
        }

        public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            try
            {
                using var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort);
                client.EnableSsl = _emailSettings.EnableSsl;
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword);

                using var message = new MailMessage();
                message.From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName);
                message.To.Add(to);
                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = isHtml;
                message.BodyEncoding = Encoding.UTF8;
                message.SubjectEncoding = Encoding.UTF8;

                await client.SendMailAsync(message);
                _logger.LogInfo($"Email sent successfully to {to}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to send email to {to}");
                return false;
            }
        }

        private string GetEmailVerificationTemplate(string fullName, string callbackUrl)
        {
            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <title>Verify Your Email</title>
                </head>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #2c3e50;'>Email Verification Required</h2>
                        <p>Hello {fullName},</p>
                        <p>Thank you for registering with our platform. To complete your registration, please verify your email address by clicking the button below:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{callbackUrl}' style='background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Verify Email Address</a>
                        </div>
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #7f8c8d;'>{callbackUrl}</p>
                        <p><strong>This link will expire in 24 hours.</strong></p>
                        <p>If you didn't create an account with us, please ignore this email.</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='font-size: 12px; color: #7f8c8d;'>This is an automated message, please do not reply to this email.</p>
                    </div>
                </body>
                </html>";
        }

        private string GetPasswordResetTemplate(string fullName, string callbackUrl)
        {
            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <title>Reset Your Password</title>
                </head>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #e74c3c;'>Password Reset Request</h2>
                        <p>Hello {fullName},</p>
                        <p>We received a request to reset your password. If you made this request, click the button below to reset your password:</p>
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{callbackUrl}' style='background-color: #e74c3c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Reset Password</a>
                        </div>
                        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                        <p style='word-break: break-all; color: #7f8c8d;'>{callbackUrl}</p>
                        <p><strong>This link will expire in 1 hour.</strong></p>
                        <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='font-size: 12px; color: #7f8c8d;'>This is an automated message, please do not reply to this email.</p>
                    </div>
                </body>
                </html>";
        }

        private string GetWelcomeTemplate(string fullName)
        {
            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <title>Welcome!</title>
                </head>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #27ae60;'>Welcome to Our Platform!</h2>
                        <p>Hello {fullName},</p>
                        <p>Welcome to our platform! Your email has been successfully verified and your account is now active.</p>
                        <p>You can now enjoy all the features our platform has to offer.</p>
                        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                        <p>Thank you for joining us!</p>
                        <hr style='border: none; border-top: 1px solid #eee; margin: 30px 0;'>
                        <p style='font-size: 12px; color: #7f8c8d;'>This is an automated message, please do not reply to this email.</p>
                    </div>
                </body>
                </html>";
        }
    }
}
