using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Domain.Helpers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;

namespace Infrastructure.Service
{
    public class FileStorageService : IFileStorageService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILoggerManager _logger;
        private readonly FileUploadSettings _fileUploadSettings;

        public FileStorageService(
            IWebHostEnvironment webHostEnvironment,
            ILoggerManager logger,
            IOptions<FileUploadSettings> fileUploadSettings)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
            _fileUploadSettings = fileUploadSettings.Value;
        }

        public async Task<FileUploadResult> UploadAvatarAsync(IFormFile file, int userId)
        {
            try
            {
                var settings = _fileUploadSettings.Avatar;

                // Validate file
                if (file == null || file.Length == 0)
                {
                    return new FileUploadResult { Success = false, ErrorMessage = "No file provided" };
                }

                if (file.Length > settings.MaxFileSizeBytes)
                {
                    return new FileUploadResult { Success = false, ErrorMessage = $"File size exceeds maximum limit of {settings.MaxFileSizeBytes / (1024 * 1024)}MB" };
                }

                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!settings.AllowedFileTypes.Contains(extension))
                {
                    return new FileUploadResult { Success = false, ErrorMessage = $"File type not allowed. Allowed types: {string.Join(", ", settings.AllowedFileTypes)}" };
                }

                if (!settings.AllowedMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
                {
                    return new FileUploadResult { Success = false, ErrorMessage = "Invalid file type" };
                }

                // Generate unique filename
                var fileName = $"avatar_{userId}_{Guid.NewGuid()}{extension}";
                var thumbnailFileName = $"thumb_{fileName}";

                // Create upload directory
                var uploadPath = Path.Combine(_webHostEnvironment.WebRootPath, settings.UploadPath);
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                var filePath = Path.Combine(uploadPath, fileName);
                var thumbnailPath = Path.Combine(uploadPath, thumbnailFileName);

                // Process and save main image
                using (var image = await Image.LoadAsync(file.OpenReadStream()))
                {
                    // Resize if necessary
                    if (image.Width > settings.MaxWidthPixels || image.Height > settings.MaxHeightPixels)
                    {
                        image.Mutate(x => x.Resize(new ResizeOptions
                        {
                            Size = new Size(settings.MaxWidthPixels, settings.MaxHeightPixels),
                            Mode = ResizeMode.Max
                        }));
                    }

                    await image.SaveAsync(filePath);
                }

                // Create thumbnail
                using (var image = await Image.LoadAsync(file.OpenReadStream()))
                {
                    image.Mutate(x => x.Resize(new ResizeOptions
                    {
                        Size = new Size(settings.ThumbnailSize, settings.ThumbnailSize),
                        Mode = ResizeMode.Crop
                    }));

                    await image.SaveAsync(thumbnailPath);
                }

                var fileUrl = $"{settings.BaseUrl}/{fileName}";
                var thumbnailUrl = $"{settings.BaseUrl}/{thumbnailFileName}";

                _logger.LogInfo($"Avatar uploaded successfully for user {userId}: {fileName}");

                return new FileUploadResult
                {
                    Success = true,
                    FileUrl = fileUrl,
                    ThumbnailUrl = thumbnailUrl,
                    FileSize = file.Length,
                    FileName = fileName
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading avatar for user {userId}");
                return new FileUploadResult { Success = false, ErrorMessage = "An error occurred while uploading the file" };
            }
        }

        public async Task<bool> DeleteAvatarAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                    return true;

                var settings = _fileUploadSettings.Avatar;
                var fileName = Path.GetFileName(fileUrl);
                var thumbnailFileName = $"thumb_{fileName}";

                var filePath = Path.Combine(_webHostEnvironment.WebRootPath, settings.UploadPath, fileName);
                var thumbnailPath = Path.Combine(_webHostEnvironment.WebRootPath, settings.UploadPath, thumbnailFileName);

                // Delete main file
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // Delete thumbnail
                if (File.Exists(thumbnailPath))
                {
                    File.Delete(thumbnailPath);
                }

                _logger.LogInfo($"Avatar deleted successfully: {fileName}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting avatar: {fileUrl}");
                return false;
            }
        }

        public async Task<bool> FileExistsAsync(string fileUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(fileUrl))
                    return false;

                var settings = _fileUploadSettings.Avatar;
                var fileName = Path.GetFileName(fileUrl);
                var filePath = Path.Combine(_webHostEnvironment.WebRootPath, settings.UploadPath, fileName);

                return File.Exists(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking file existence: {fileUrl}");
                return false;
            }
        }

        public string GetFullUrl(string relativePath)
        {
            // This would typically include the domain/base URL
            // For now, return the relative path as-is
            return relativePath;
        }
    }
}
