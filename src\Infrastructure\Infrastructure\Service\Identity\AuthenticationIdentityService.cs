﻿using Domain.Helpers;
using Microsoft.AspNetCore.Identity;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Domain.Entities.Users;
using Microsoft.Extensions.Caching.Distributed;
using Abstraction.Constants;
using Application.Common.Extensions;
using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Identity;
using Abstraction.Contracts.Service;


namespace Infrastructure.Identity.Implementations
{
    public class AuthenticationIdentityService : IAuthenticationService
    {
        #region Fileds
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;
        private readonly JwtSettings _jwtSettings;
        private readonly ILoggerManager _logger;
        private readonly IDistributedCache _distributedCache;
        private readonly ITokenBlacklistService _tokenBlacklistService;
        #endregion

        #region Constructors
        public AuthenticationIdentityService(UserManager<User> userManager, RoleManager<Role> roleManager, JwtSettings jwtSettings, ILoggerManager logger, IDistributedCache distributedCache, ITokenBlacklistService tokenBlacklistService)
        {
            _userManager = userManager;
            _jwtSettings = jwtSettings;
            _logger = logger;
            _roleManager = roleManager;
            _distributedCache = distributedCache;
            _tokenBlacklistService = tokenBlacklistService;
        }

        #endregion

        #region Main_Functions

        public async Task<JwtAuthResponse> GetJwtToken(User user)
        {
            try
            {
                //Generate Jwt Token..
                var (jwtToken, accessToken) = await GenerateJwtToken(user);

                //Generate Resfresh Token
                var refreshToken = RefreshToken(user.UserName!);
                //Save To Redis
                var userRefreshToken = new UserRefreshToken
                {
                    AddedTime = DateTime.UtcNow,
                    ExpiryDate = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpireDate),
                    IsUsed = true,
                    IsRevoked = false,
                    JwtId = jwtToken.Id,
                    RefreshToken = refreshToken.TokenString,
                    Token = accessToken,
                    UserId = user.Id
                };
                await Create(userRefreshToken);

                //return response
                var response = new JwtAuthResponse
                {
                    AccessToken = accessToken,
                    refreshToken = refreshToken
                };

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetJwtToken");
                throw;
            }
        }
        public async Task<JwtAuthResponse> GetRefreshToken(User user, JwtSecurityToken jwtToken, DateTime? expiryDate, string refreshToken)
        {
            try
            {
                var (jwtSecurityToken, newToken) = await GenerateJwtToken(user);
                var refreshTokenResult = new RefreshToken();
                refreshTokenResult.UserName = jwtToken.Claims.FirstOrDefault(x => x.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name")!.Value;
                refreshTokenResult.TokenString = refreshToken;
                refreshTokenResult.ExpireAt = (DateTime)expiryDate;

                var response = new JwtAuthResponse();
                response.AccessToken = newToken;
                response.refreshToken = refreshTokenResult;

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetRefreshToken");
                throw;
            }

        }
        public JwtSecurityToken ReadJwtToken(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ArgumentNullException(nameof(accessToken));
            }
            var handler = new JwtSecurityTokenHandler();
            var response = handler.ReadJwtToken(accessToken);
            return response;
        }
        public async Task<(string, DateTime?)> ValidateDetails(JwtSecurityToken jwtToken, string accessToken, string refreshTken)
        {
            try
            {
                if (jwtToken == null || !jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256Signature))
                {
                    return ("AlgorithmIsWrong", null);
                }
                if (jwtToken.ValidTo > DateTime.UtcNow)
                {
                    return ("TokenIsRunning", null);
                }
                //Get UserId From Glaims in jwtToken
                var userId = jwtToken.Claims.FirstOrDefault(x => x.Type == "Id")!.Value;
                var userRefreshtoken = await GetById(int.Parse(userId));
                if (userRefreshtoken == null)
                {
                    return ("RefreshTokenNotFound", null);
                }
                var expiryDate = userRefreshtoken.ExpiryDate;
                return (userId, expiryDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateDetails");
                throw;
            }
        }
        public async Task<string> ValidateJwtToken(string accessToken)
        {
            try
            {
                var handler = new JwtSecurityTokenHandler();
                var parameters = new TokenValidationParameters
                {
                    ValidateIssuer = _jwtSettings.ValidateIssure,
                    ValidIssuers = new[] { _jwtSettings.Issure },
                    ValidateIssuerSigningKey = _jwtSettings.ValidateIssureSigningKey,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_jwtSettings.Secret)),
                    ValidateAudience = _jwtSettings.validateAudience,
                    ValidAudience = _jwtSettings.Audience,
                    ValidateLifetime = _jwtSettings.ValidateLifeTime
                };
                var validator = handler.ValidateToken(accessToken, parameters, out SecurityToken validatedToken);
                if (validator == null)
                {
                    return "InvalidJwtToken";
                }
                return "NotExpired";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ValidateJwtToken");
                throw;
            }
        }
        #endregion

        #region Sub-Functions
        private async Task<(JwtSecurityToken, string)> GenerateJwtToken(User user)
        {
            var roleNames = await _userManager.GetRolesAsync(user);
            var Claims = await GetClaims(user, roleNames.ToList());

            // Add JTI claim for token tracking
            var jti = Guid.NewGuid().ToString();
            Claims.Add(new Claim(JwtRegisteredClaimNames.Jti, jti));
            Claims.Add(new Claim(JwtRegisteredClaimNames.Iat,
                new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(),
                ClaimValueTypes.Integer64));

            var key = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_jwtSettings.Secret));
            var _CredentialS = new SigningCredentials(key, SecurityAlgorithms.HmacSha256Signature);

            var expiry = DateTime.UtcNow.AddDays(_jwtSettings.AccessTokenExpireDate);
            var jwtToken = new JwtSecurityToken(
                _jwtSettings.Issure,
                _jwtSettings.Audience,
                claims: Claims,
                expires: expiry,
                signingCredentials: _CredentialS
                );

            var accessToken = new JwtSecurityTokenHandler().WriteToken(jwtToken);

            // Track token for blacklisting purposes
            try
            {
                // Use reflection to call TrackUserTokenAsync if it exists
                var method = _tokenBlacklistService.GetType().GetMethod("TrackUserTokenAsync");
                if (method != null)
                {
                    await (Task)method.Invoke(_tokenBlacklistService, new object[] { user.Id, jti, expiry });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to track token {jti} for user {user.Id}");
                // Don't fail token generation if tracking fails
            }

            return (jwtToken, accessToken);
        }
        private async Task<List<Claim>> GetClaims(User user, List<string> roles)
        {
            //Add some properties to claims...
            var claims = new List<Claim>()
            {
                new Claim(ClaimTypes.Name,user.UserName!),
                new Claim(ClaimTypes.NameIdentifier,user.Id.ToString()),
                new Claim(ClaimTypes.Email,user.Email!),
                new Claim(nameof(UserClaimsModel.Id),user.Id.ToString()),

            };

            //Add roles to claims...
            foreach (var roleName in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, roleName));
                var _role = await _roleManager.FindByNameAsync(roleName);
                if (_role != null)
                {
                    var roleClaims = await _roleManager.GetClaimsAsync(_role);
                    foreach (Claim roleClaim in roleClaims)
                    {
                        claims.Add(new Claim(CustomClaimTypes.Permission, roleClaim.Value));
                    }
                }
            }
            return claims;
        }
        private async Task<UserRefreshToken?> GetById(int userId)
        {
            string key = $"userrefreshtoken-{userId}";
            if (!_distributedCache.TryGetValue(key, out UserRefreshToken? _userRefreshToken))
            {
                return null;
            }
            return _userRefreshToken;
        }
        private async Task<UserRefreshToken?> Create(UserRefreshToken userRefreshToken)
        {
            string key = $"userrefreshtoken-{userRefreshToken.UserId}";
            var cacheEntryOptions = new DistributedCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromSeconds(3600))
                .SetAbsoluteExpiration(TimeSpan.FromSeconds(3600));
            await _distributedCache.SetAsync(key, userRefreshToken, cacheEntryOptions);

            if (!_distributedCache.TryGetValue(key, out UserRefreshToken? _userRefreshToken))
            {
                return null;
            }
            return _userRefreshToken;
        }
        private RefreshToken RefreshToken(string username)
        {
            var refreshToken = new RefreshToken
            {
                ExpireAt = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpireDate),
                UserName = username,
                TokenString = GenerateRefreshToken()
            };
            return refreshToken;
        }
        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            var randomNumberGenerate = RandomNumberGenerator.Create();
            randomNumberGenerate.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }
        #endregion
    }
}
