﻿using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Abstraction.Contracts.Service.Catalog;
using AutoMapper;
using Infrastructure.Service.Catalog;

namespace Infrastructure.Service
{
    public class ServiceManager : IServiceManager
    {
        private readonly Lazy<IProductService> _productService;
        private readonly IGenericRepository _repository;
        private readonly IMapper _mapper;

        public ServiceManager(IGenericRepository repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;

            _productService = new Lazy<IProductService>(() => new ProductService(_repository, _mapper));

        }
        public IProductService ProductService => _productService.Value;
    }
}
