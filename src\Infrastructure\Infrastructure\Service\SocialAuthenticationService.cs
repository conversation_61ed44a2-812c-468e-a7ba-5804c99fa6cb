using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Repository;
using Abstraction.Contracts.Service;
using Domain.Entities.Users;
using Domain.Helpers;
using Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace Infrastructure.Service
{
    public class SocialAuthenticationService : ISocialAuthenticationService
    {
        private readonly AppDbContext _context;
        private readonly ILoggerManager _logger;
        private readonly OAuthSettings _oauthSettings;
        private readonly HttpClient _httpClient;

        public SocialAuthenticationService(
            AppDbContext context,
            ILoggerManager logger,
            IOptions<OAuthSettings> oauthSettings,
            HttpClient httpClient)
        {
            _context = context;
            _logger = logger;
            _oauthSettings = oauthSettings.Value;
            _httpClient = httpClient;
        }

        public async Task<string> GetAuthorizationUrlAsync(string provider, string state, string? returnUrl = null)
        {
            try
            {
                return provider.ToLower() switch
                {
                    "google" => BuildGoogleAuthUrl(state, returnUrl),
                    "facebook" => BuildFacebookAuthUrl(state, returnUrl),
                    "microsoft" => BuildMicrosoftAuthUrl(state, returnUrl),
                    _ => throw new ArgumentException($"Unsupported provider: {provider}")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error building authorization URL for provider: {provider}");
                return string.Empty;
            }
        }

        public async Task<SocialUserInfo?> GetUserInfoAsync(string provider, string code, string state)
        {
            try
            {
                return provider.ToLower() switch
                {
                    "google" => await GetGoogleUserInfoAsync(code),
                    "facebook" => await GetFacebookUserInfoAsync(code),
                    "microsoft" => await GetMicrosoftUserInfoAsync(code),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting user info from provider: {provider}");
                return null;
            }
        }

        public async Task<UserExternalLogin?> CreateOrUpdateExternalLoginAsync(User user, SocialUserInfo socialUserInfo, string provider)
        {
            try
            {
                var existingLogin = await _context.UserExternalLogins
                    .FirstOrDefaultAsync(x => x.UserId == user.Id && 
                                            x.SocialProvider.Name.ToLower() == provider.ToLower());

                var socialProvider = await GetOrCreateSocialProviderAsync(provider);

                if (existingLogin != null)
                {
                    // Update existing login
                    existingLogin.ProviderKey = socialUserInfo.Id;
                    existingLogin.Email = socialUserInfo.Email;
                    existingLogin.FirstName = socialUserInfo.FirstName;
                    existingLogin.LastName = socialUserInfo.LastName;
                    existingLogin.ProfilePictureUrl = socialUserInfo.ProfilePictureUrl;
                    existingLogin.AccessToken = socialUserInfo.AccessToken;
                    existingLogin.RefreshToken = socialUserInfo.RefreshToken;
                    existingLogin.TokenExpiresAt = socialUserInfo.TokenExpiresAt;
                    existingLogin.LastLoginAt = DateTime.UtcNow;

                    _context.UserExternalLogins.Update(existingLogin);
                }
                else
                {
                    // Create new login
                    existingLogin = new UserExternalLogin
                    {
                        UserId = user.Id,
                        SocialProviderId = socialProvider.Id,
                        ProviderKey = socialUserInfo.Id,
                        ProviderDisplayName = provider,
                        Email = socialUserInfo.Email,
                        FirstName = socialUserInfo.FirstName,
                        LastName = socialUserInfo.LastName,
                        ProfilePictureUrl = socialUserInfo.ProfilePictureUrl,
                        AccessToken = socialUserInfo.AccessToken,
                        RefreshToken = socialUserInfo.RefreshToken,
                        TokenExpiresAt = socialUserInfo.TokenExpiresAt,
                        CreatedAt = DateTime.UtcNow,
                        LastLoginAt = DateTime.UtcNow,
                        IsActive = true
                    };

                    _context.UserExternalLogins.Add(existingLogin);
                }

                await _context.SaveChangesAsync();
                return existingLogin;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating/updating external login for provider: {provider}");
                return null;
            }
        }

        public async Task<User?> FindUserByExternalLoginAsync(string provider, string providerKey)
        {
            try
            {
                var externalLogin = await _context.UserExternalLogins
                    .Include(x => x.User)
                    .Include(x => x.SocialProvider)
                    .FirstOrDefaultAsync(x => x.SocialProvider.Name.ToLower() == provider.ToLower() && 
                                            x.ProviderKey == providerKey && 
                                            x.IsActive);

                return externalLogin?.User;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error finding user by external login for provider: {provider}");
                return null;
            }
        }

        #region Private Helper Methods

        private string BuildGoogleAuthUrl(string state, string? returnUrl)
        {
            var settings = _oauthSettings.Google;
            var baseUrl = "https://accounts.google.com/o/oauth2/v2/auth";
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = settings.ClientId,
                ["redirect_uri"] = settings.RedirectUri,
                ["response_type"] = "code",
                ["scope"] = settings.Scope,
                ["state"] = state,
                ["access_type"] = "offline",
                ["prompt"] = "consent"
            };

            return BuildUrl(baseUrl, parameters);
        }

        private string BuildFacebookAuthUrl(string state, string? returnUrl)
        {
            var settings = _oauthSettings.Facebook;
            var baseUrl = "https://www.facebook.com/v18.0/dialog/oauth";
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = settings.AppId,
                ["redirect_uri"] = settings.RedirectUri,
                ["response_type"] = "code",
                ["scope"] = settings.Scope,
                ["state"] = state
            };

            return BuildUrl(baseUrl, parameters);
        }

        private string BuildMicrosoftAuthUrl(string state, string? returnUrl)
        {
            var settings = _oauthSettings.Microsoft;
            var baseUrl = $"https://login.microsoftonline.com/{settings.TenantId}/oauth2/v2.0/authorize";
            var parameters = new Dictionary<string, string>
            {
                ["client_id"] = settings.ClientId,
                ["redirect_uri"] = settings.RedirectUri,
                ["response_type"] = "code",
                ["scope"] = settings.Scope,
                ["state"] = state,
                ["response_mode"] = "query"
            };

            return BuildUrl(baseUrl, parameters);
        }

        private string BuildUrl(string baseUrl, Dictionary<string, string> parameters)
        {
            var queryString = string.Join("&", parameters.Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value)}"));
            return $"{baseUrl}?{queryString}";
        }

        private async Task<SocialUserInfo?> GetGoogleUserInfoAsync(string code)
        {
            // Implementation for Google OAuth token exchange and user info retrieval
            // This is a simplified version - in production, you'd implement full OAuth flow
            return null;
        }

        private async Task<SocialUserInfo?> GetFacebookUserInfoAsync(string code)
        {
            // Implementation for Facebook OAuth token exchange and user info retrieval
            return null;
        }

        private async Task<SocialUserInfo?> GetMicrosoftUserInfoAsync(string code)
        {
            // Implementation for Microsoft OAuth token exchange and user info retrieval
            return null;
        }

        private async Task<SocialProvider> GetOrCreateSocialProviderAsync(string providerName)
        {
            var provider = await _context.SocialProviders
                .FirstOrDefaultAsync(x => x.Name.ToLower() == providerName.ToLower());

            if (provider == null)
            {
                provider = new SocialProvider
                {
                    Name = providerName,
                    DisplayName = providerName,
                    IsEnabled = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.SocialProviders.Add(provider);
                await _context.SaveChangesAsync();
            }

            return provider;
        }

        #endregion
    }
}
