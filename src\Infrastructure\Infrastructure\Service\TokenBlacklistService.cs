using Abstraction.Contracts.Logger;
using Abstraction.Contracts.Service;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Infrastructure.Service
{
    public class TokenBlacklistService : ITokenBlacklistService
    {
        private readonly IDistributedCache _cache;
        private readonly ILoggerManager _logger;
        private const string BLACKLIST_PREFIX = "blacklist:";
        private const string USER_TOKENS_PREFIX = "user_tokens:";

        public TokenBlacklistService(IDistributedCache cache, ILoggerManager logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task BlacklistTokenAsync(string jti, DateTime expiry)
        {
            try
            {
                var key = $"{BLACKLIST_PREFIX}{jti}";
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpiration = expiry
                };

                await _cache.SetStringAsync(key, "blacklisted", options);
                _logger.LogInfo($"Token {jti} blacklisted until {expiry}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to blacklist token {jti}");
                throw;
            }
        }

        public async Task<bool> IsTokenBlacklistedAsync(string jti)
        {
            try
            {
                var key = $"{BLACKLIST_PREFIX}{jti}";
                var result = await _cache.GetStringAsync(key);
                return !string.IsNullOrEmpty(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to check blacklist status for token {jti}");
                // In case of error, assume token is not blacklisted to avoid blocking valid users
                return false;
            }
        }

        public async Task CleanupExpiredTokensAsync()
        {
            // Redis automatically handles expiration, so this is mainly for logging
            _logger.LogInfo("Token cleanup completed (Redis handles automatic expiration)");
            await Task.CompletedTask;
        }

        public async Task BlacklistAllUserTokensAsync(int userId)
        {
            try
            {
                var userTokensKey = $"{USER_TOKENS_PREFIX}{userId}";
                var tokensJson = await _cache.GetStringAsync(userTokensKey);
                
                if (!string.IsNullOrEmpty(tokensJson))
                {
                    var tokens = JsonSerializer.Deserialize<List<UserTokenInfo>>(tokensJson);
                    if (tokens != null)
                    {
                        foreach (var token in tokens)
                        {
                            await BlacklistTokenAsync(token.Jti, token.Expiry);
                        }
                    }
                }

                // Clear user tokens list
                await _cache.RemoveAsync(userTokensKey);
                _logger.LogInfo($"All tokens for user {userId} have been blacklisted");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to blacklist all tokens for user {userId}");
                throw;
            }
        }

        public async Task<bool> IsUserTokenValidAsync(int userId, string jti)
        {
            try
            {
                // First check if token is blacklisted
                if (await IsTokenBlacklistedAsync(jti))
                    return false;

                // Check if token exists in user's active tokens
                var userTokensKey = $"{USER_TOKENS_PREFIX}{userId}";
                var tokensJson = await _cache.GetStringAsync(userTokensKey);
                
                if (string.IsNullOrEmpty(tokensJson))
                    return true; // If no tracking, assume valid

                var tokens = JsonSerializer.Deserialize<List<UserTokenInfo>>(tokensJson);
                return tokens?.Any(t => t.Jti == jti && t.Expiry > DateTime.UtcNow) ?? true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to validate token {jti} for user {userId}");
                return true; // In case of error, assume token is valid to avoid blocking users
            }
        }

        public async Task TrackUserTokenAsync(int userId, string jti, DateTime expiry)
        {
            try
            {
                var userTokensKey = $"{USER_TOKENS_PREFIX}{userId}";
                var tokensJson = await _cache.GetStringAsync(userTokensKey);
                
                var tokens = string.IsNullOrEmpty(tokensJson) 
                    ? new List<UserTokenInfo>() 
                    : JsonSerializer.Deserialize<List<UserTokenInfo>>(tokensJson) ?? new List<UserTokenInfo>();

                // Remove expired tokens
                tokens.RemoveAll(t => t.Expiry <= DateTime.UtcNow);

                // Add new token
                tokens.Add(new UserTokenInfo { Jti = jti, Expiry = expiry });

                var options = new DistributedCacheEntryOptions
                {
                    SlidingExpiration = TimeSpan.FromDays(30) // Keep user token list for 30 days
                };

                await _cache.SetStringAsync(userTokensKey, JsonSerializer.Serialize(tokens), options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to track token {jti} for user {userId}");
                // Don't throw here as this is not critical for authentication
            }
        }

        private class UserTokenInfo
        {
            public string Jti { get; set; } = null!;
            public DateTime Expiry { get; set; }
        }
    }
}
