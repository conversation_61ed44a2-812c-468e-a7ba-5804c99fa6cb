using System.Security.Cryptography;
using System.Text;
using Abstraction.Contracts.Service;

namespace Infrastructure.Service
{
    public class TokenService : ITokenService
    {
        public string GenerateSecureToken(int length = 32)
        {
            using var rng = RandomNumberGenerator.Create();
            var bytes = new byte[length];
            rng.GetBytes(bytes);
            return Convert.ToBase64String(bytes)
                .Replace("+", "-")
                .Replace("/", "_")
                .Replace("=", "");
        }

        public string GenerateEmailVerificationToken()
        {
            return GenerateSecureToken(32);
        }

        public string GeneratePasswordResetToken()
        {
            return GenerateSecureToken(32);
        }

        public bool ValidateToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            // Basic validation - check if it's a valid base64url string
            try
            {
                var normalizedToken = token.Replace("-", "+").Replace("_", "/");
                
                // Add padding if necessary
                switch (normalizedToken.Length % 4)
                {
                    case 2: normalizedToken += "=="; break;
                    case 3: normalizedToken += "="; break;
                }

                Convert.FromBase64String(normalizedToken);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
