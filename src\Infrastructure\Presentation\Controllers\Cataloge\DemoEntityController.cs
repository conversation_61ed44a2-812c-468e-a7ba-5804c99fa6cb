﻿using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using Domain.Entities.Products;
using Infrastructure.Dto.DemoEntity;
using Abstraction.Contracts.Service.Catalog;
using Microsoft.AspNetCore.Authorization;
using Abstraction.Base.Dto;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Microsoft.AspNetCore.Http;

namespace Controllers.Cataloge
{
    [Route("api/DemoEntities/[action]")]
    [ApiController]
    public class StrategyController : BaseController
    {

        IDemoEntityService _DemoEntityService;
        public StrategyController(IDemoEntityService DemoEntityService)
        {
            _DemoEntityService = DemoEntityService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public virtual async Task<IActionResult> CreateDemoEntity([FromBody] DemoEntityDto entity)
        {
            var returnValue = await _DemoEntityService.AddAsync(entity);
            return NewResult(returnValue);
        }

        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public virtual async Task<IActionResult> UpdateStrategy([FromBody] DemoEntityDto entity)
        {
            var returnValue = await _DemoEntityService.UpdateAsync(entity);
            return NewResult(returnValue);
        }
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<DemoEntityDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetStrategyById(int id)
        {
            var returnValue = await _DemoEntityService.GetByIdAsync<DemoEntityDto>(id, false);
            return NewResult(returnValue);
        }

        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<DemoEntityDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> StrategyList([FromQuery] BaseListDto query)
        {
            var returnValue = await _DemoEntityService.GetAllPagedAsync<DemoEntityDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }
    }
}
