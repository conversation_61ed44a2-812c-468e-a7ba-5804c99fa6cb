﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Application.Features.Identity.Authentications.Commands.SignIn;
using Application.Features.Identity.Authentications.Commands.RefreshToken;
using Application.Features.Identity.Authentications.Commands.Register;
using Application.Features.Identity.Authentications.Commands.VerifyEmail;
using Application.Features.Identity.Authentications.Commands.ResendVerification;
using Application.Features.Identity.Authentications.Commands.ForgotPassword;
using Application.Features.Identity.Authentications.Commands.ResetPassword;
using Application.Features.Identity.Authentications.Commands.SignOut;
using Application.Features.Identity.Authentications.Commands.SocialLogin;
using Microsoft.AspNetCore.Authorization;
using Application.Features.Identity.Authentications.Queries.ValidateAccessToken;
using Presentation.Bases;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Domain.Helpers;


namespace Identity.Controllers
{
    [Route("api/Users/<USER>")]
    [ApiController]
    [AllowAnonymous]
    public class AuthenticationController : AppControllerBase
    {

        [HttpPost("Sign-In")]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SignIn([FromBody] SignInCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
       

        [HttpPost("Refresh-Token")]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Register")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> Register([FromBody] RegisterUserCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Verify-Email")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> VerifyEmail([FromQuery] string email, [FromQuery] string token)
        {
            var command = new VerifyEmailCommand { Email = email, Token = token };
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Resend-Verification")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ResendVerification([FromBody] ResendVerificationCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Forgot-Password")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Reset-Password")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Sign-Out")]
        [Authorize]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> SignOut([FromBody] SignOutCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Social-Login/Initiate")]
        [ProducesResponseType(typeof(BaseResponse<SocialLoginInitiationResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<SocialLoginInitiationResponse>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> InitiateSocialLogin([FromBody] InitiateSocialLoginCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpGet("Social-Login/{provider}/Callback")]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SocialLoginCallback(
            string provider,
            [FromQuery] string code,
            [FromQuery] string state,
            [FromQuery] string? error = null,
            [FromQuery] string? error_description = null)
        {
            var command = new SocialLoginCallbackCommand
            {
                Provider = provider,
                Code = code,
                State = state,
                Error = error,
                ErrorDescription = error_description
            };

            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpGet("Is-Valid_Token")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> IsValidToken([FromBody] AccessTokenQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

    }
}
