using Application.Features.Identity.Profile.Commands.ChangeAvatar;
using Application.Features.Identity.Profile.Commands.ChangePassword;
using Application.Features.Identity.Profile.Commands.UpdateProfile;
using Application.Features.Identity.Profile.Queries.GetProfile;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using System.Security.Claims;
using Abstraction.Base.Response;

namespace Presentation.Controllers.Identity
{
    [Route("api/Users/<USER>")]
    [ApiController]
    [Authorize]
    public class ProfileController : AppControllerBase
    {
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<GetProfileResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<GetProfileResponse>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(BaseResponse<GetProfileResponse>), StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetProfile()
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var query = new GetProfileQuery { UserId = userId.Value };
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<UpdateProfileResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<UpdateProfileResponse>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponse<UpdateProfileResponse>), StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var command = new UpdateProfileCommand
            {
                UserId = userId.Value,
                FullName = request.FullName,
                Email = request.Email,
                PhoneNumber = request.PhoneNumber,
                Address = request.Address,
                Country = request.Country,
                Bio = request.Bio,
                DateOfBirth = request.DateOfBirth
            };

            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Change-Password")]
        [ProducesResponseType(typeof(BaseResponse<ChangePasswordResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<ChangePasswordResponse>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponse<ChangePasswordResponse>), StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var command = new ChangePasswordCommand
            {
                UserId = userId.Value,
                CurrentPassword = request.CurrentPassword,
                NewPassword = request.NewPassword,
                ConfirmPassword = request.ConfirmPassword,
                InvalidateAllTokens = request.InvalidateAllTokens
            };

            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPost("Avatar")]
        [ProducesResponseType(typeof(BaseResponse<ChangeAvatarResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<ChangeAvatarResponse>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(BaseResponse<ChangeAvatarResponse>), StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ChangeAvatar([FromForm] ChangeAvatarRequest request)
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var command = new ChangeAvatarCommand
            {
                UserId = userId.Value,
                AvatarFile = request.AvatarFile
            };

            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            return null;
        }
    }

    // Request DTOs
    public class UpdateProfileRequest
    {
        public string FullName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string? PhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? Country { get; set; }
        public string? Bio { get; set; }
        public DateTime? DateOfBirth { get; set; }
    }

    public class ChangePasswordRequest
    {
        public string CurrentPassword { get; set; } = null!;
        public string NewPassword { get; set; } = null!;
        public string ConfirmPassword { get; set; } = null!;
        public bool InvalidateAllTokens { get; set; } = true;
    }

    public class ChangeAvatarRequest
    {
        public IFormFile AvatarFile { get; set; } = null!;
    }
}
