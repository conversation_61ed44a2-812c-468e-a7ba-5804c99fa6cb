﻿using Microsoft.AspNetCore.Mvc;
using Application.Features.Identity.Users.Commands.EditUser;
using Application.Features.Identity.Users.Commands.AddUser;
using Application.Features.Identity.Users.Commands.ChangePassword;
using Application.Features.Identity.Users.Queries.List;
using Application.Features.Identity.Users.Commands.DeleteUser;
using Presentation.Bases;
using Application.Features.Identity.Users.Queries.Get;
using Application.Features.Identity.Users.Commands.EditUserRoles;
using Application.Features.Identity.Users.Queries.GetUserRoles;


namespace Identity.Controllers
{
    [Route("api/Users/<USER>/[Action]")]
    [ApiController]
    //[Authorize(Roles = "superadmin")]
    public class UserManagementController : AppControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> List([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return Ok(response);
        }

        [HttpGet]
        public async Task<IActionResult> GetUserById(int id)
        {
            var response = await Mediator.Send(new GetUserQuery() { Id = id });
            return NewResult(response);
        }

        [HttpPost]
        public async Task<IActionResult> AddUser([FromBody] AddUserCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateUser([FromBody] EditUserCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpGet]
        public async Task<IActionResult> UserRoles(int id)
        {
            var response = await Mediator.Send(new GetUserRolesQuery { Id = id });
            return NewResult(response);
        }


        [HttpPut]
        public async Task<IActionResult> UpdateUserRoles([FromBody] EditUserRolesCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteUser(int id)
        {
            var response = await Mediator.Send(new DeleteUserCommand() { Id = id });
            return NewResult(response);
        }

        [HttpPut]
        public async Task<IActionResult> ChangePasswordForUser([FromBody] ChangePasswordCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

      
    }
}
