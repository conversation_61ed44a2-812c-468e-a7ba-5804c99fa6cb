﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>False</GeneratePackageOnBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" />
    <PackageReference Include="NLog" />
	  <PackageReference Include="Asp.Versioning.Mvc" />
	  <PackageReference Include="Marvin.Cache.Headers" />
	  <PackageReference Include="NuGet.CommandLine">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Swashbuckle.AspNetCore" />
	  <PackageReference Include="AspNetCoreRateLimit" />
	  <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Core\Abstraction\Abstraction.csproj" />
    <ProjectReference Include="..\..\Core\Application\Application.csproj" />
    <ProjectReference Include="..\..\Infrastructure\Infrastructure\Infrastructure.csproj" />
    <ProjectReference Include="..\..\Infrastructure\Presentation\Presentation.csproj" />
  </ItemGroup>
	 
</Project>
