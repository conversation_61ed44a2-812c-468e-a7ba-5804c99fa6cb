{"AllowedOrigins": "http://localhost:4200", "RedisInstanceName": "localhost", "ConnectionStrings": {"default": "Server=localhost; Database=ArabDtdb; TrustServerCertificate=true; Trusted_Connection=true", "Redis": "localhost:6379"}, "jwtSettings": {"secret": "9832yfdfsdkfhskduurc&%*&(&fkdfcweechwlcwjjjskflsdif;aniodskdjfwiokfjs", "issure": "JadwaIdentityProject", "audience": "webSite", "validateIssure": true, "validateAudience": true, "validateLifeTime": true, "validateIssureSigningKey": true, "accessTokenExpireDate": 1, "refreshTokenExpireDate": 30}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "ArabDt Platform", "BaseUrl": "https://localhost:7010"}, "SecuritySettings": {"EmailVerificationTokenExpiryHours": 24, "PasswordResetTokenExpiryHours": 1, "MaxPasswordResetAttemptsPerHour": 3, "MaxRegistrationAttemptsPerHour": 5, "MaxEmailVerificationAttemptsPerHour": 3, "RequireEmailConfirmation": true, "TokenLength": 32}}