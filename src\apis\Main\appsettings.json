{"AllowedOrigins": "http://localhost:4200", "RedisInstanceName": "localhost", "ConnectionStrings": {"default": "Data Source=ArabDt.db", "Redis": "localhost:6379"}, "jwtSettings": {"secret": "9832yfdfsdkfhskduurc&%*&(&fkdfcweechwlcwjjjskflsdif;aniodskdjfwiokfjs", "issure": "JadwaIdentityProject", "audience": "webSite", "validateIssure": true, "validateAudience": true, "validateLifeTime": true, "validateIssureSigningKey": true, "accessTokenExpireDate": 1, "refreshTokenExpireDate": 30}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "EnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "ArabDt Platform", "BaseUrl": "https://localhost:7012"}, "SecuritySettings": {"EmailVerificationTokenExpiryHours": 24, "PasswordResetTokenExpiryHours": 1, "MaxPasswordResetAttemptsPerHour": 3, "MaxRegistrationAttemptsPerHour": 5, "MaxEmailVerificationAttemptsPerHour": 3, "RequireEmailConfirmation": true, "TokenLength": 32}, "OAuthSettings": {"Google": {"Enabled": false, "ClientId": "", "ClientSecret": "", "RedirectUri": "https://localhost:7012/api/Users/<USER>/Social-Login/Google/Callback", "Scope": "openid profile email"}, "Facebook": {"Enabled": false, "AppId": "", "AppSecret": "", "RedirectUri": "https://localhost:7012/api/Users/<USER>/Social-Login/Facebook/Callback", "Scope": "email,public_profile"}, "Microsoft": {"Enabled": false, "ClientId": "", "ClientSecret": "", "RedirectUri": "https://localhost:7012/api/Users/<USER>/Social-Login/Microsoft/Callback", "Scope": "openid profile email", "TenantId": "common"}}}