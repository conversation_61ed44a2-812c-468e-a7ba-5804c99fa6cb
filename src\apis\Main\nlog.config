﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" autoReload="true" internalLogLevel="Trace" internalLogFile=".\internal_logs\internallog.txt">
  <targets>
<target name="logfile" xsi:type="File" fileName="C:\Workspace\ArabDt.BuildingBlocks.V2\src\apis\Main\logs\${shortdate}_logfile.txt" layout="${longdate} ${level:uppercase=true} ${message}"/>

    <!--<target xsi:type="Database"
           name="dbTarget" 
           connectionString="Data Source=tcp:taskserver123.database.windows.net; database=CustomerFlowRegisteration; User ID=dbadmin;Password=***********"
           commandText="INSERT INTO [Logs](Date,Message,Message,Exception,Trace,Logger) VALUES (GetDate(),@msg,@level,@exception,@trace,@logger)">
      <parameter name="@msg" layout="${message}" />
      <parameter name="@level" layout="${level}" />
      <parameter name="@exception" layout="${exception}" />
      <parameter name="@trace" layout="${trace}" />
      <parameter name="@logger" layout="${logger}" />
    </target>-->
   
  
  </targets>
 
  <rules>
   
    <!--<logger name="*" minlevel="Error" levels="Debug,Info,Error,Warn,Fatal" writeTo="dbTarget" />-->
     <logger name="*" minlevel="Error" levels="Debug,Info,Error,Warn,Fatal"  writeTo="logfile" /> 
  </rules>
</nlog>