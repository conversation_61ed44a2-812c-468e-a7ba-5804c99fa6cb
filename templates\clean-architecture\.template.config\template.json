{"$schema": "http://json.schemastore.org/template", "author": "ArabDt", "classifications": ["Clean Architecture"], "name": "Clean Architecture Solution Use Case", "description": "Create a new use case (query or command)", "identity": "Clean.Architecture.Solution.UseCase.CSharp", "groupIdentity": "Clean.Architecture.Solution.UseCase", "shortName": "ca", "tags": {"language": "C#", "type": "item"}, "sourceName": "CleanArchitectureUseCase", "preferNameDirectory": false, "symbols": {"DefaultNamespace": {"type": "bind", "binding": "msbuild:RootNamespace", "replaces": "CleanArchitecture", "defaultValue": "CleanArchitecture"}, "featureName": {"type": "parameter", "datatype": "string", "isRequired": true, "replaces": "FeatureName", "fileRename": "FeatureName"}, "returnType": {"type": "parameter", "datatype": "string", "isRequired": true, "replaces": "object", "defaultValue": "object", "fileRename": "object"}, "facadeType": {"type": "parameter", "datatype": "choice", "isRequired": true, "replaces": "facadeType", "choices": [{"choice": "Service", "description": "configure service as manager"}, {"choice": "Repository", "description": "configure repository as manager"}], "description": "The type of manager"}, "createRepository": {"type": "computed", "value": "(facadeType == \"Repository\")"}, "createService": {"type": "computed", "value": "(facadeType == \"Service\")"}, "createEntity": {"type": "parameter", "datatype": "bool", "defaultValue": "false", "description": "create entity"}}, "sources": [{"modifiers": [{"condition": "(createRepository)", "exclude": ["Abstraction/Service/**", "Infrastructure/Service/**"]}, {"condition": "(createService)", "exclude": ["Abstraction/Repository/**", "Infrastructure/Repository/**"]}, {"condition": "(createEntity)", "rename": {"Domain/Entities/object.cs": "Core/Domain/Entities/object.cs"}}, {"condition": "(createRepository)", "rename": {"Abstraction/Repository/IobjectRepository.cs": "Core/Abstraction/Contract/Repository/IobjectRepository.cs", "Infrastructure/Repository/objectRepository.cs": "Infrastructure/Infrastructure/Repository/objectRepository.cs"}}, {"condition": "(createService)", "rename": {"Abstraction/Service/IobjectService.cs": "Core/Abstraction/Contract/Service/IobjectService.cs", "Infrastructure/Service/objectService.cs": "Infrastructure/Infrastructure/Service/objectService.cs"}}, {"condition": "true", "rename": {"Application/FeatureName": "Core/Application/Features/FeatureName", "Application/Mapping/FeatureName": "Core/Application/Mapping/FeatureName", "Abstraction/Constants/ModulePermissions/objectPermission.cs": "Core/Abstraction/Constants/ModulePermissions/objectPermission.cs", "Presentation/Controllers/objectController.cs": "Infrastructure/Presentation/Controllers/objectController.cs"}}]}]}