﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities;
#if (createService)
using Abstraction.Contracts.Service;
#else
using Abstraction.Contracts.Repository;
#endif



namespace Application.Features.FeatureName.Commands.Add
{
    public class AddobjectCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, ICommandHandler<AddobjectCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IfacadeTypeManager _facadeType;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public AddobjectCommandHandler(IfacadeTypeManager facadeType, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _facadeType = facadeType;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(AddobjectCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                #if (createService)
                return await _facadeType.objectService.AddAsync<AddobjectCommand>(request);     
                #else
                var _resultmapper = _mapper.Map<object>(request);
                var result = await _facadeType.FeatureName.AddAsync(_resultmapper);
                if (result is null)
                    return BadRequest<string>("Added Operation Failed.");
                return Success("Added Operation Successfully.");
                #endif

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in AddobjectCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion


    }
}
