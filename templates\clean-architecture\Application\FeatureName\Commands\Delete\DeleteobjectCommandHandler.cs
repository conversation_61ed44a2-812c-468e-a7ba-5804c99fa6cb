﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Domain.Entities;
using AutoMapper;
#if (createService)
using Abstraction.Contracts.Service;
#else
using Abstraction.Contracts.Repository;
#endif



namespace Application.Features.FeatureName.Commands.Delete
{
    public class DeleteobjectCommandHandler : BaseResponse<PERSON><PERSON><PERSON>, ICommandHandler<DeleteobjectCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IfacadeTypeManager _facadeType;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public DeleteobjectCommandHandler(IfacadeTypeManager facadeType, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _facadeType = facadeType;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(DeleteobjectCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                #if (createService)
                return await _facadeType.objectService.DeleteAsync(request.Id);
                #else
                var entity = await _facadeType.FeatureName.GetByIdAsync<object>(request.Id, false);
                var status = await _facadeType.FeatureName.DeleteAsync(entity);
                if (!status)
                    return BadRequest<string>("Delete Operation Failed.");
                return Success("Delete Operation Successfully.");
                #endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditobjectCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion

    }
}
