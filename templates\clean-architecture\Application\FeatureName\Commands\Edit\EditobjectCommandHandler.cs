﻿using Abstraction.Contracts.Logger;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using AutoMapper;
using Domain.Entities;
#if (createService)
using Abstraction.Contracts.Service;
#else
using Abstraction.Contracts.Repository;
#endif

namespace Application.Features.FeatureName.Commands.Edit
{
    public class EditobjectCommandHandler : Base<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON>, ICommandHandler<EditobjectCommand, BaseResponse<string>>
    {

        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IfacadeTypeManager _facadeType;
        private readonly IMapper _mapper;
        #endregion

        #region Constructors
        public EditobjectCommandHandler(IfacadeTypeManager facadeType, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _facadeType = facadeType;
            _mapper = mapper;
        }
        #endregion

        #region Handle Functions
        public async Task<BaseResponse<string>> Handle(EditobjectCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (request == null)
                    return BadRequest<string>("the request can't be blank");
                #if (createService)
                return await _facadeType.objectService.UpdateAsync<EditobjectCommand>(request);
                #else
                var originalEntity = await _facadeType.FeatureName.GetByIdAsync<object>(request.Id, true);
                _mapper.Map(request, originalEntity);
                var status = await _facadeType.FeatureName.UpdateAsync(originalEntity);
                if (!status)
                    return BadRequest<string>("Update Operation Failed.");
                return Success("Update Operation Successfully.");
                #endif
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in EditobjectCommand");
                return ServerError<string>(ex.Message);
            }
        }

        #endregion

    }
}
