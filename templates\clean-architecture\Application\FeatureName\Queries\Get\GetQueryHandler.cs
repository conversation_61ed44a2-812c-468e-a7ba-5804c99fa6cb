﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Application.Features.FeatureName.Dtos;
using Abstraction.Contracts.Logger;
#if (createService)
using Abstraction.Contracts.Service;
#else
using Abstraction.Contracts.Repository;
#endif


namespace Application.Features.FeatureName.Queries.Get
{
    public class GetQueryHandler : BaseResponse<PERSON><PERSON><PERSON>, IQueryHandler<GetQuery, BaseResponse<SingleobjectResponse>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IfacadeTypeManager _facadeType;
        private readonly IMapper _mapper;
        #endregion

        #region Constructor(s)
        public GetQueryHandler(IfacadeTypeManager facadeType, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _facadeType = facadeType;
            _mapper = mapper;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<SingleobjectResponse>> Handle(GetQuery request, CancellationToken cancellationToken)
        {
            try
            {   
                #if (createService)
                var result = await _facadeType.objectService.GetByIdAsync<SingleobjectResponse>(request.Id, false);
                #else
                var result = await _facadeType.FeatureName.GetByIdAsync<SingleobjectResponse>(request.Id, false);
                #endif
                if (result == null)
                    return NotFound<SingleobjectResponse>("object with this Id not found!");
                var resultMapper = _mapper.Map<SingleobjectResponse>(result);
                return Success(resultMapper);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in GetResultByIdQuery");
                return ServerError<SingleobjectResponse>(ex.Message);
            }
        }

        #endregion
    }
}
