﻿using AutoMapper;
using Application.Base.Abstracts;
using Abstraction.Base.Response;
using Abstraction.Common.Wappers;
using Application.Features.FeatureName.Dtos;
using Abstraction.Contracts.Logger;
#if (createService)
using Abstraction.Contracts.Service;
#else
using Abstraction.Contracts.Repository;
#endif


namespace Application.Features.FeatureName.Queries.List
{
    public class ListQueryHandler : BaseResponseHandler, IQueryHandler<ListQuery, BaseResponse<PaginatedResult<SingleobjectResponse>>>
    {
        #region Fileds
        private readonly ILoggerManager _logger;
        private readonly IfacadeTypeManager _facadeType;
        private readonly IMapper _mapper;
        #endregion

        #region Constructor(s)
        public ListQueryHandler(IfacadeTypeManager facadeType, IMapper mapper, ILoggerManager logger)
        {
            _logger = logger;
            _facadeType = facadeType;
            _mapper = mapper;
        }
        #endregion

        #region Functions
        public async Task<BaseResponse<PaginatedResult<SingleobjectResponse>>> Handle(ListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                #if (createService)
                 var result = _facadeType.objectService.GetAllAsync<object>(false);
                #else
                var result = _facadeType.FeatureName.GetAll<SingleobjectResponse>(false);
                #endif
                if (!result.Any())
                {
                    return EmptyCollection(PaginatedResult<SingleobjectResponse>.Success(new List<SingleobjectResponse>(), 0, 0, 0));
                }
                var objectList = await _mapper.ProjectTo<SingleobjectResponse>(result).ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);
                return Success(objectList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error: in ListQuery");
                return ServerError<PaginatedResult<SingleobjectResponse>>(ex.Message);
            }
        }
        #endregion
    }
}
