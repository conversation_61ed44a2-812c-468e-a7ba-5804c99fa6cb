﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Application.Features.FeatureName.Commands.Edit;
using Application.Features.FeatureName.Commands.Add;
using Application.Features.FeatureName.Queries.List;
using Application.Features.FeatureName.Queries.Get;
using Application.Features.FeatureName.Commands.Delete;
using Presentation.Bases;
using Abstraction.Constants.ModulePermissions;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Abstraction.Common.Wappers;
using Application.Features.Categories.Dtos;

namespace Presentation.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class objectController : AppControllerBase
    {
        [Authorize(Policy = objectPermission.List)]
        [HttpGet("List")]
		[ProducesResponseType(typeof(PaginatedResult<SingleobjectResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFeatureNamePaginatedList([FromQuery] ListQuery query)
        {
            var response = await Mediator.Send(query);
            return Ok(response);
        }

        [Authorize(Policy = objectPermission.View)]
        [HttpGet("GetById")]
		[ProducesResponseType(typeof(BaseResponse<SingleobjectResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFeatureNameById([FromRoute] int id)
        {
            var response = await Mediator.Send(new GetQuery() { Id = id });
            return NewResult(response);
        }

        [Authorize(Policy = objectPermission.Create)]
		[HttpPost("Add")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Addobject([FromBody] AddobjectCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [Authorize(Policy = objectPermission.Edit)]
        [HttpPut("Edit")]
		[ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Editobject([FromBody] EditobjectCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [Authorize(Policy = objectPermission.Delete)]
        [HttpDelete("Delete")]
		[ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Deleteobject([FromRoute] int id)
        {
            var response = await Mediator.Send(new DeleteobjectCommand() { Id = id });
            return NewResult(response);
        }



    }
}
