{"$schema": "http://json.schemastore.org/template", "author": "ArabDt", "classifications": ["Onion Architecture"], "name": "Onion Architecture Solution Use Case", "description": "Create a new use case using onion architecture", "identity": "Onion.Architecture.Solution.UseCase.CSharp", "groupIdentity": "Onion.Architecture.Solution.UseCase", "shortName": "oa", "tags": {"language": "C#", "type": "item"}, "sourceName": "OnionArchitectureUseCase", "preferNameDirectory": false, "symbols": {"DefaultNamespace": {"type": "bind", "binding": "msbuild:RootNamespace", "replaces": "OnionArchitecture", "defaultValue": "OnionArchitecture"}, "featureName": {"type": "parameter", "datatype": "string", "isRequired": true, "replaces": "FeatureName", "fileRename": "FeatureName"}, "returnType": {"type": "parameter", "datatype": "string", "isRequired": true, "replaces": "object", "defaultValue": "object", "fileRename": "object"}, "createEntity": {"type": "parameter", "datatype": "bool", "defaultValue": "false", "description": "create entity"}}, "sources": [{"modifiers": [{"condition": "true", "rename": {"Abstraction/Service/IobjectService.cs": "Core/Abstraction/Contract/Service/IobjectService.cs", "Abstraction/Constants/ModulePermissions/objectPermission.cs": "Core/Abstraction/Constants/ModulePermissions/objectPermission.cs", "Infrastructure/Dto/FeatureName/objectDto.cs": "Infrastructure/Infrastructure/Dto/FeatureName/objectDto.cs", "Infrastructure/Dto/FeatureName/objectValidation.cs": "Infrastructure/Infrastructure/Dto/FeatureName/objectValidation.cs", "Infrastructure/Dto/FeatureName/FeatureNameProfile.cs": "Infrastructure/Infrastructure/Dto/FeatureName/FeatureNameProfile.cs", "Infrastructure/Service/objectService.cs": "Infrastructure/Infrastructure/Service/objectService.cs", "Presentation/Controllers/objectController.cs": "Infrastructure/Presentation/Controllers/objectController.cs"}}, {"condition": "(createEntity)", "rename": {"Domain/Entities/object.cs": "Core/Domain/Entities/object.cs"}}]}]}