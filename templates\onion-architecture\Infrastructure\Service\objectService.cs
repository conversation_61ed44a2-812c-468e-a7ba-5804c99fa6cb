﻿using Domain.Entities.Products;
using AutoMapper;
using Abstraction.Contracts.Service.Catalog;
using Abstraction.Contracts.Repository;
using Infrastructure.Service;
using Domain.Entities;
using Abstraction.Contracts.Service;

namespace Infrastructure.Onion.Service
{
    public class objectService : BaseService<object>, IobjectService
    {
        public objectService(IGenericRepository repository, IMapper mapper) : base(repository, mapper)
        {
            _repository = repository;
        }
    }
}
