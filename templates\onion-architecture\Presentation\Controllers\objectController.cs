﻿using Microsoft.AspNetCore.Mvc;
using Presentation.Bases;
using Abstraction.Contracts.Service;
using Infrastructure.Dto.FeatureName;
using Abstraction.Common.Wappers;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Abstraction.Base.Dto;
namespace Presentation.Controllers
{
    [Route("api/FeatureName/[action]")]
    [ApiController]
    public class objectController : BaseController 
    {
        
        
        IobjectService _service;
        public objectController(IobjectService service)
        {
            _service = service;
        }
        [Authorize(Policy = objectPermission.Create)]
        [HttpPost]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public virtual async Task<IActionResult> Create([FromBody] objectDto entity)
        {
            var returnValue = await _service.AddAsync(entity);
            return NewResult(returnValue);
        }
        [Authorize(Policy = objectPermission.Edit)]
        [HttpPut]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status422UnprocessableEntity)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public virtual async Task<IActionResult> Update([FromBody] objectDto entity)
        {
            var returnValue = await _service.UpdateAsync(entity);
            return NewResult(returnValue);
        }
		[Authorize(Policy = objectPermission.View)]
        [HttpGet]
        [ProducesResponseType(typeof(BaseResponse<objectDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetById(int id)
        {
            var returnValue = await _service.GetByIdAsync<objectDto>(id, false);
            return NewResult(returnValue);
        }
        [Authorize(Policy = objectPermission.List)]
        [HttpGet]
        [ProducesResponseType(typeof(PaginatedResult<objectDto>),StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> List([FromQuery] BaseListDto query)
        {
            var returnValue = await _service.GetAllPagedAsync<objectDto>(query.PageNumber, query.PageSize, query.OrderBy, false);
            return NewResult(returnValue);
        }
    }
}
